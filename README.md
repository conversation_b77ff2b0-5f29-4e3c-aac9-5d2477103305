# WiFi File Transfer

A simple Android app that allows you to transfer files wirelessly between your phone and PC using a local HTTP server.

## Features

- **File Upload**: Upload files from PC to phone via web browser
- **File Download**: Download files from phone to PC
- **Simple Interface**: Clean, modern UI built with Jetpack Compose
- **No Internet Required**: Works on local WiFi network only
- **Cross-Platform**: Works with any web browser on PC

## How to Use

1. **Start the Server**: Open the app and tap "Start Server"
2. **Get the Address**: The app will display the server address (e.g., `http://*************:8080`)
3. **Open in Browser**: On your PC, open the displayed address in any web browser
4. **Transfer Files**: Use the web interface to upload or download files

## Requirements

- Android 8.0 (API 26) or higher
- Both devices must be on the same WiFi network
- No internet connection required

## Permissions

The app requires the following permissions:
- `INTERNET`: To run the HTTP server
- `ACCESS_NETWORK_STATE`: To check network connectivity
- `ACCESS_WIFI_STATE`: To get WiFi information
- Storage permissions: To read/write files (handled automatically based on Android version)

## Recent Fixes and Improvements

### 1. Network Security Configuration
- Added `android:usesCleartextTraffic="true"` to allow HTTP connections
- Created `network_security_config.xml` to allow cleartext traffic for local network addresses
- Fixed issues with Android 9+ blocking cleartext traffic

### 2. Better Error Handling
- Added permission checks before starting server
- Improved server startup/shutdown with proper resource cleanup
- Added port availability checking
- Better error messages and user feedback

### 3. UI Improvements
- Enhanced server status display with visual indicators
- Better layout with cards and proper spacing
- Improved error state handling

### 4. Server Robustness
- Added proper socket cleanup to prevent resource leaks
- Improved file upload handling for large files
- Better multipart form data parsing
- Added fallback mechanisms for failed uploads

### 5. File Management
- Files are saved to the Downloads/WiFiTransfer directory
- Automatic directory creation with proper error handling
- Sample files created for testing

## Technical Details

- **Port**: 8080 (configurable in code)
- **Protocol**: HTTP (not HTTPS for simplicity)
- **File Storage**: Downloads/WiFiTransfer directory
- **Framework**: Jetpack Compose for UI
- **Server**: Custom HTTP server implementation

## Troubleshooting

### Connection Issues
- Ensure both devices are on the same WiFi network
- Try the "Restart" button if connection fails
- Check firewall settings on PC
- Verify the IP address is correct

### Permission Issues
- Grant all requested permissions when prompted
- For Android 13+, ensure media permissions are granted

### File Upload Issues
- Try smaller files first to test
- Check available storage space
- Ensure the Downloads directory is accessible

## Development

This project is built with:
- Kotlin
- Jetpack Compose
- Android SDK
- Gradle

To build the project:
```bash
./gradlew build
```

## License

This project is open source and available under the MIT License. 
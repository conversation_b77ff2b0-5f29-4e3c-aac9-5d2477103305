package com.xeno.wififiletransfer

import android.Manifest
import android.annotation.SuppressLint
import android.content.pm.PackageManager
import android.os.Bundle
import android.os.Environment
import android.widget.Toast
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.core.app.ActivityCompat
import androidx.core.content.ContextCompat
import java.io.*
import java.net.*
import java.text.SimpleDateFormat
import java.util.*

class MainActivity : ComponentActivity() {
    private var httpServer: RobustHttpServer? = null
    private val PERMISSION_REQUEST_CODE = 123

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        try {
            setContent {
                WiFiTransferApp(
                    onStartServer = { startServer() },
                    onStopServer = { stopServer() },
                    onRestartServer = { restartServer() }
                )
            }

            // Request permissions after UI is set up
            requestPermissions()
        } catch (e: Exception) {
            e.printStackTrace()
            Toast.makeText(this, "Error initializing app: ${e.message}", Toast.LENGTH_LONG).show()
        }
    }

    private fun requestPermissions() {
        val permissions = mutableListOf<String>()

        if (ContextCompat.checkSelfPermission(this, Manifest.permission.INTERNET) != PackageManager.PERMISSION_GRANTED) {
            permissions.add(Manifest.permission.INTERNET)
        }
        if (ContextCompat.checkSelfPermission(this, Manifest.permission.ACCESS_NETWORK_STATE) != PackageManager.PERMISSION_GRANTED) {
            permissions.add(Manifest.permission.ACCESS_NETWORK_STATE)
        }
        if (ContextCompat.checkSelfPermission(this, Manifest.permission.ACCESS_WIFI_STATE) != PackageManager.PERMISSION_GRANTED) {
            permissions.add(Manifest.permission.ACCESS_WIFI_STATE)
        }

        // Handle storage permissions based on Android version
        if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.TIRAMISU) {
            // Android 13+ - use granular media permissions
            if (ContextCompat.checkSelfPermission(this, Manifest.permission.READ_MEDIA_IMAGES) != PackageManager.PERMISSION_GRANTED) {
                permissions.add(Manifest.permission.READ_MEDIA_IMAGES)
            }
            if (ContextCompat.checkSelfPermission(this, Manifest.permission.READ_MEDIA_VIDEO) != PackageManager.PERMISSION_GRANTED) {
                permissions.add(Manifest.permission.READ_MEDIA_VIDEO)
            }
            if (ContextCompat.checkSelfPermission(this, Manifest.permission.READ_MEDIA_AUDIO) != PackageManager.PERMISSION_GRANTED) {
                permissions.add(Manifest.permission.READ_MEDIA_AUDIO)
            }
        } else {
            // Android 12 and below - use READ_EXTERNAL_STORAGE and WRITE_EXTERNAL_STORAGE
            if (ContextCompat.checkSelfPermission(this, Manifest.permission.READ_EXTERNAL_STORAGE) != PackageManager.PERMISSION_GRANTED) {
                permissions.add(Manifest.permission.READ_EXTERNAL_STORAGE)
            }
            if (ContextCompat.checkSelfPermission(this, Manifest.permission.WRITE_EXTERNAL_STORAGE) != PackageManager.PERMISSION_GRANTED) {
                permissions.add(Manifest.permission.WRITE_EXTERNAL_STORAGE)
            }
        }

        if (permissions.isNotEmpty()) {
            ActivityCompat.requestPermissions(this, permissions.toTypedArray(), PERMISSION_REQUEST_CODE)
        }
    }

    private fun startServer() {
        try {
            if (httpServer == null) {
                // Check if we have necessary permissions before starting server
                if (!hasRequiredPermissions()) {
                    Toast.makeText(this, "Please grant all required permissions first", Toast.LENGTH_LONG).show()
                    requestPermissions()
                    return
                }
                
                httpServer = RobustHttpServer(8080)
                httpServer?.start()
                Toast.makeText(this, "Server started on port 8080", Toast.LENGTH_SHORT).show()
            } else {
                Toast.makeText(this, "Server is already running", Toast.LENGTH_SHORT).show()
            }
        } catch (e: Exception) {
            e.printStackTrace()
            Toast.makeText(this, "Failed to start server: ${e.message}", Toast.LENGTH_LONG).show()
        }
    }

    private fun hasRequiredPermissions(): Boolean {
        return ContextCompat.checkSelfPermission(this, Manifest.permission.INTERNET) == PackageManager.PERMISSION_GRANTED &&
               ContextCompat.checkSelfPermission(this, Manifest.permission.ACCESS_NETWORK_STATE) == PackageManager.PERMISSION_GRANTED &&
               ContextCompat.checkSelfPermission(this, Manifest.permission.ACCESS_WIFI_STATE) == PackageManager.PERMISSION_GRANTED
    }

    private fun stopServer() {
        try {
            httpServer?.stop()
            httpServer = null
            Toast.makeText(this, "Server stopped", Toast.LENGTH_SHORT).show()
        } catch (e: Exception) {
            e.printStackTrace()
            Toast.makeText(this, "Error stopping server: ${e.message}", Toast.LENGTH_SHORT).show()
        }
    }

    private fun restartServer() {
        try {
            stopServer()
            Thread.sleep(1000) // Wait a second before restarting
            startServer()
            Toast.makeText(this, "Server restarted", Toast.LENGTH_SHORT).show()
        } catch (e: Exception) {
            e.printStackTrace()
            Toast.makeText(this, "Error restarting server: ${e.message}", Toast.LENGTH_SHORT).show()
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        try {
            stopServer()
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    // Public method to get debug information
    fun getServerDebugInfo(): String {
        val info = httpServer?.getDownloadDirectoryInfo() ?: "Server not available"
        println("=== DEBUG INFO REQUESTED ===")
        println(info)
        println("============================")
        return info
    }
}

@Composable
fun WiFiTransferApp(
    onStartServer: () -> Unit,
    onStopServer: () -> Unit,
    onRestartServer: () -> Unit
) {
    LocalContext.current
    var isServerRunning by remember { mutableStateOf(false) }
    var ipAddress by remember { mutableStateOf("Not connected") }
    val uploadedFiles by remember { mutableStateOf(listOf<FileInfo>()) }

    LaunchedEffect(Unit) {
        try {
            ipAddress = getWiFiIPAddress()
        } catch (e: Exception) {
            e.printStackTrace()
            ipAddress = "Error getting IP"
        }
    }

    Column(
        modifier = Modifier
            .fillMaxSize()
            .padding(16.dp),
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Text(
            text = "WiFi File Transfer",
            fontSize = 24.sp,
            fontWeight = FontWeight.Bold,
            modifier = Modifier.padding(bottom = 32.dp)
        )

        Card(
            modifier = Modifier
                .fillMaxWidth()
                .padding(bottom = 16.dp)
        ) {
            Column(
                modifier = Modifier.padding(16.dp),
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                Text(
                    text = "Server Address:",
                    fontSize = 16.sp,
                    fontWeight = FontWeight.Medium
                )
                Text(
                    text = "http://$ipAddress:8080",
                    fontSize = 18.sp,
                    fontWeight = FontWeight.Bold,
                    color = MaterialTheme.colorScheme.primary,
                    modifier = Modifier.padding(top = 8.dp)
                )
            }
        }

        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(bottom = 16.dp),
            horizontalArrangement = Arrangement.SpaceEvenly
        ) {
            Button(
                onClick = {
                    if (!isServerRunning) {
                        onStartServer()
                        isServerRunning = true
                    }
                },
                enabled = !isServerRunning
            ) {
                Text("Start Server")
            }

            Button(
                onClick = {
                    if (isServerRunning) {
                        onStopServer()
                        isServerRunning = false
                    }
                },
                enabled = isServerRunning
            ) {
                Text("Stop Server")
            }

            Button(
                onClick = {
                    onRestartServer()
                    isServerRunning = true
                }
            ) {
                Text("Restart")
            }

            Spacer(modifier = Modifier.width(8.dp))

            val context = LocalContext.current
            Button(
                onClick = {
                    // Debug: Show directory info
                    val activity = context as? MainActivity
                    val info = activity?.getServerDebugInfo() ?: "Server not available"
                    Toast.makeText(context, info, Toast.LENGTH_LONG).show()
                    println(info)
                }
            ) {
                Text("Debug Files")
            }
        }

        if (isServerRunning) {
            Card(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(bottom = 16.dp),
                colors = CardDefaults.cardColors(
                    containerColor = MaterialTheme.colorScheme.primaryContainer
                )
            ) {
                Row(
                    modifier = Modifier.padding(16.dp),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Text(
                        text = "✓ Server is running",
                        color = MaterialTheme.colorScheme.onPrimaryContainer,
                        fontWeight = FontWeight.Medium
                    )
                }
            }
        }

        Text(
            text = "Instructions:",
            fontSize = 18.sp,
            fontWeight = FontWeight.Bold,
            modifier = Modifier.padding(bottom = 8.dp)
        )

        Card(
            modifier = Modifier
                .fillMaxWidth()
                .padding(bottom = 16.dp)
        ) {
            Text(
                text = "1. Make sure your PC and phone are on the same WiFi network\n" +
                        "2. Start the server above\n" +
                        "3. Open the server address in your PC's web browser\n" +
                        "4. Upload files from PC or download files from phone\n\n" +
                        "💡 If you get connection errors, try the Restart button",
                modifier = Modifier.padding(16.dp)
            )
        }

        if (uploadedFiles.isNotEmpty()) {
            Text(
                text = "Received Files:",
                fontSize = 16.sp,
                fontWeight = FontWeight.Medium,
                modifier = Modifier.padding(bottom = 8.dp)
            )

            LazyColumn {
                items(uploadedFiles) { file ->
                    Card(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(vertical = 4.dp)
                    ) {
                        Column(
                            modifier = Modifier.padding(12.dp)
                        ) {
                            Text(
                                text = file.name,
                                fontWeight = FontWeight.Medium
                            )
                            Text(
                                text = "Size: ${formatFileSize(file.size)} | ${file.timestamp}",
                                fontSize = 12.sp,
                                color = MaterialTheme.colorScheme.onSurfaceVariant
                            )
                        }
                    }
                }
            }
        }
    }
}

data class FileInfo(
    val name: String,
    val size: Long,
    val timestamp: String
)

fun getWiFiIPAddress(): String {
    return try {
        // Modern approach using NetworkInterface (works on all Android versions)
        val networkInterfaces = NetworkInterface.getNetworkInterfaces()
        for (networkInterface in networkInterfaces) {
            if (!networkInterface.isLoopback && networkInterface.isUp) {
                val addresses = networkInterface.inetAddresses
                for (address in addresses) {
                    if (!address.isLoopbackAddress && address is Inet4Address) {
                        val hostAddress = address.hostAddress
                        // Check if this looks like a local network address
                        if (hostAddress != null && (
                            hostAddress.startsWith("192.168.") ||
                            hostAddress.startsWith("10.") ||
                            hostAddress.startsWith("172.16.") ||
                            hostAddress.startsWith("172.17.") ||
                            hostAddress.startsWith("172.18.") ||
                            hostAddress.startsWith("172.19.") ||
                            hostAddress.startsWith("172.20.") ||
                            hostAddress.startsWith("172.21.") ||
                            hostAddress.startsWith("172.22.") ||
                            hostAddress.startsWith("172.23.") ||
                            hostAddress.startsWith("172.24.") ||
                            hostAddress.startsWith("172.25.") ||
                            hostAddress.startsWith("172.26.") ||
                            hostAddress.startsWith("172.27.") ||
                            hostAddress.startsWith("172.28.") ||
                            hostAddress.startsWith("172.29.") ||
                            hostAddress.startsWith("172.30.") ||
                            hostAddress.startsWith("172.31.")
                        )) {
                            return hostAddress
                        }
                    }
                }
            }
        }

        // If no private network address found, return any non-loopback IPv4 address
        val networkInterfaces2 = NetworkInterface.getNetworkInterfaces()
        for (networkInterface in networkInterfaces2) {
            if (!networkInterface.isLoopback && networkInterface.isUp) {
                val addresses = networkInterface.inetAddresses
                for (address in addresses) {
                    if (!address.isLoopbackAddress && address is Inet4Address) {
                        val hostAddress = address.hostAddress
                        if (hostAddress != null && !hostAddress.startsWith("127.")) {
                            return hostAddress
                        }
                    }
                }
            }
        }

        "Not connected to network"
    } catch (e: Exception) {
        e.printStackTrace()
        "Error getting IP: ${e.message}"
    }
}

@SuppressLint("DefaultLocale")
fun formatFileSize(bytes: Long): String {
    val kb = 1024
    val mb = kb * 1024
    val gb = mb * 1024

    return when {
        bytes >= gb -> String.format("%.1f GB", bytes.toDouble() / gb)
        bytes >= mb -> String.format("%.1f MB", bytes.toDouble() / mb)
        bytes >= kb -> String.format("%.1f KB", bytes.toDouble() / kb)
        else -> "$bytes B"
    }
}

class RobustHttpServer(
    private val port: Int
) {
    private var serverSocket: ServerSocket? = null
    private var isRunning = false
    private val clientThreads = mutableSetOf<Thread>()
    private val maxConcurrentConnections = 10
    private val connectionTimeout = 30000 // 30 seconds
    private val readTimeout = 60000 // 60 seconds for file uploads

    // Use Downloads directory for better user access
    private val downloadDir: File = File(Environment.getExternalStoragePublicDirectory(Environment.DIRECTORY_DOWNLOADS), "WiFiTransfer")

    init {
        initializeServer()
    }

    private fun initializeServer() {
        try {
            println("=== Initializing Robust HTTP Server ===")
            println("Download directory: ${downloadDir.absolutePath}")

            // Create download directory with proper error handling
            if (!downloadDir.exists()) {
                println("Creating download directory...")
                val created = downloadDir.mkdirs()
                if (!created && !downloadDir.exists()) {
                    throw IOException("Failed to create download directory: ${downloadDir.absolutePath}")
                }
                println("Download directory created successfully")
            } else {
                println("Download directory already exists")
            }

            // Verify directory permissions
            if (!downloadDir.canWrite()) {
                println("WARNING: Download directory is not writable: ${downloadDir.absolutePath}")
                // Try to make it writable
                if (!downloadDir.setWritable(true)) {
                    throw IOException("Cannot make download directory writable: ${downloadDir.absolutePath}")
                }
            }
            println("Download directory is writable")
            
            // Create sample files to demonstrate the functionality
            try {
                val readmeFile = File(downloadDir, "README.txt")
                println("Checking README.txt: ${readmeFile.absolutePath}")
                println("README.txt exists: ${readmeFile.exists()}")
                
                if (!readmeFile.exists()) {
                    println("Creating README.txt...")
                    val readmeContent = "Welcome to WiFi File Transfer!\n\nThis directory contains files available for download.\n\nFiles uploaded from your PC will appear here."
                    readmeFile.writeText(readmeContent, Charsets.UTF_8)
                    println("README.txt created successfully (${readmeFile.length()} bytes)")
                } else {
                    println("README.txt already exists (${readmeFile.length()} bytes)")
                }
                
                // Verify the file is readable
                if (!readmeFile.canRead()) {
                    println("WARNING: README.txt is not readable!")
                } else {
                    println("README.txt is readable")
                }
            } catch (e: Exception) {
                println("ERROR creating README.txt: ${e.message}")
                e.printStackTrace()
                // Don't throw here, continue with other files
            }

            try {
                val sampleFile = File(downloadDir, "sample_document.txt")
                println("Checking sample_document.txt: ${sampleFile.absolutePath}")
                println("sample_document.txt exists: ${sampleFile.exists()}")
                
                if (!sampleFile.exists()) {
                    println("Creating sample_document.txt...")
                    val sampleContent = "This is a sample document.\nYou can download this file to test the download functionality.\n\nCreated: ${SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.getDefault()).format(Date())}"
                    sampleFile.writeText(sampleContent, Charsets.UTF_8)
                    println("sample_document.txt created successfully (${sampleFile.length()} bytes)")
                } else {
                    println("sample_document.txt already exists (${sampleFile.length()} bytes)")
                }
                
                // Verify the file is readable
                if (!sampleFile.canRead()) {
                    println("WARNING: sample_document.txt is not readable!")
                } else {
                    println("sample_document.txt is readable")
                }
            } catch (e: Exception) {
                println("ERROR creating sample_document.txt: ${e.message}")
                e.printStackTrace()
                // Don't throw here, continue
            }
            
            println("=== HTTP Server initialization completed ===")
        } catch (e: Exception) {
            println("ERROR during HTTP server initialization: ${e.message}")
            e.printStackTrace()
            // Don't throw RuntimeException - let the server start anyway
            // The server can still function without sample files
            println("Server will start without sample files")
        }
    }

    fun start() {
        synchronized(this) {
            if (isRunning) {
                println("Server is already running")
                return
            }

            try {
                println("=== Starting Robust HTTP Server ===")

                // Test port availability
                if (!isPortAvailable(port)) {
                    throw IOException("Port $port is already in use")
                }

                // Create and configure server socket
                serverSocket = ServerSocket().apply {
                    reuseAddress = true
                    soTimeout = 1000 // Accept timeout for clean shutdown
                    bind(InetSocketAddress(port))
                }

                isRunning = true
                println("HTTP Server started successfully on port $port")
                println("Server accessible at: http://0.0.0.0:$port")

                // Start server thread
                Thread(::runServer, "HttpServer-Main").start()

            } catch (e: Exception) {
                println("Failed to start HTTP server: ${e.message}")
                e.printStackTrace()
                isRunning = false
                serverSocket?.close()
                serverSocket = null
                throw e
            }
        }
    }

    private fun runServer() {
        try {
            while (isRunning && serverSocket?.isClosed == false) {
                try {
                    val clientSocket = serverSocket?.accept()
                    if (clientSocket != null && isRunning) {
                        // Check connection limit
                        cleanupFinishedThreads()
                        if (clientThreads.size >= maxConcurrentConnections) {
                            println("Max connections reached, rejecting client: ${clientSocket.remoteSocketAddress}")
                            clientSocket.close()
                            continue
                        }

                        // Handle client in separate thread
                        val clientThread = Thread({
                            handleClientSafely(clientSocket)
                        }, "HttpServer-Client-${System.currentTimeMillis()}")

                        clientThreads.add(clientThread)
                        clientThread.start()
                    }
                } catch (e: SocketTimeoutException) {
                    // Normal timeout, continue loop
                    continue
                } catch (e: Exception) {
                    if (isRunning) {
                        println("Error accepting client connection: ${e.message}")
                        e.printStackTrace()
                        Thread.sleep(100) // Brief pause before retrying
                    }
                }
            }
        } catch (e: Exception) {
            if (isRunning) {
                println("Server thread error: ${e.message}")
                e.printStackTrace()
            }
        } finally {
            println("Server thread exiting")
        }
    }

    private fun isPortAvailable(port: Int): Boolean {
        return try {
            ServerSocket().use { socket ->
                socket.bind(InetSocketAddress(port))
                true
            }
        } catch (e: Exception) {
            false
        }
    }

    private fun cleanupFinishedThreads() {
        clientThreads.removeAll { !it.isAlive }
    }

    fun stop() {
        synchronized(this) {
            if (!isRunning) {
                println("Server is not running")
                return
            }

            try {
                println("=== Stopping Robust HTTP Server ===")
                isRunning = false

                // Close server socket to stop accepting new connections
                serverSocket?.close()

                // Wait for existing client connections to finish (with timeout)
                val shutdownStart = System.currentTimeMillis()
                val maxShutdownTime = 5000 // 5 seconds

                while (clientThreads.isNotEmpty() &&
                       (System.currentTimeMillis() - shutdownStart) < maxShutdownTime) {
                    cleanupFinishedThreads()
                    if (clientThreads.isNotEmpty()) {
                        Thread.sleep(100)
                    }
                }

                // Force close any remaining client threads
                if (clientThreads.isNotEmpty()) {
                    println("Force closing ${clientThreads.size} remaining client connections")
                    clientThreads.forEach { thread ->
                        try {
                            thread.interrupt()
                        } catch (e: Exception) {
                            println("Error interrupting client thread: ${e.message}")
                        }
                    }
                    clientThreads.clear()
                }

                println("HTTP Server stopped successfully")

            } catch (e: Exception) {
                println("Error stopping server: ${e.message}")
                e.printStackTrace()
            } finally {
                serverSocket = null
                isRunning = false
                clientThreads.clear()
            }
        }
    }

    private fun handleClientSafely(clientSocket: Socket) {
        val clientAddress = clientSocket.remoteSocketAddress.toString()
        println("=== New client connected: $clientAddress ===")

        try {
            // Configure socket with appropriate timeouts
            clientSocket.soTimeout = readTimeout
            clientSocket.tcpNoDelay = true
            clientSocket.keepAlive = false

            println("Socket configured for $clientAddress")

            clientSocket.getInputStream().use { inputStream ->
                clientSocket.getOutputStream().use { outputStream ->
                    println("Starting HTTP request handling for $clientAddress")
                    handleHttpRequest(inputStream, outputStream, clientAddress)
                    println("Completed HTTP request handling for $clientAddress")
                }
            }

        } catch (e: SocketTimeoutException) {
            println("Client timeout: $clientAddress")
        } catch (e: IOException) {
            println("Client I/O error: $clientAddress - ${e.message}")
            e.printStackTrace()
        } catch (e: Exception) {
            println("Client error: $clientAddress - ${e.message}")
            e.printStackTrace()
        } finally {
            try {
                clientSocket.close()
                println("=== Client disconnected: $clientAddress ===")
            } catch (e: Exception) {
                println("Error closing client socket: ${e.message}")
            }
        }
    }

    private fun handleHttpRequest(inputStream: InputStream, outputStream: OutputStream, clientAddress: String) {
        try {
            // Parse HTTP request with timeout protection
            val request = parseHttpRequest(inputStream)
            if (request == null) {
                println("Invalid HTTP request from $clientAddress")
                sendErrorResponse(outputStream, 400, "Bad Request")
                return
            }

            println("$clientAddress: ${request.method} ${request.path}")

            // Route request to appropriate handler
            println("Routing request: ${request.method} ${request.path}")
            when (request.method) {
                "GET" -> {
                    println("Handling GET request for ${request.path}")
                    if (request.path == "/test") {
                        // Simple test endpoint
                        val testResponse = """{"status": "ok", "message": "Server is working!", "timestamp": ${System.currentTimeMillis()}}"""
                        sendJsonResponse(outputStream, 200, testResponse)
                    } else {
                        handleGetRequest(request.path, outputStream)
                    }
                }
                "POST" -> {
                    println("Handling POST request for ${request.path}")
                    handlePostRequest(request, inputStream, outputStream)
                }
                "OPTIONS" -> {
                    println("Handling OPTIONS request")
                    handleOptionsRequest(outputStream)
                }
                else -> {
                    println("Unknown method: ${request.method}")
                    sendErrorResponse(outputStream, 405, "Method Not Allowed")
                }
            }

        } catch (e: Exception) {
            println("Error processing request from $clientAddress: ${e.message}")
            e.printStackTrace()
            try {
                sendErrorResponse(outputStream, 500, "Internal Server Error")
            } catch (responseError: Exception) {
                println("Error sending error response: ${responseError.message}")
            }
        }
    }

    data class HttpRequest(
        val method: String,
        val path: String,
        val headers: Map<String, String>,
        val contentLength: Int = 0
    )

    private fun parseHttpRequest(inputStream: InputStream): HttpRequest? {
        return try {
            // Read request line byte by byte to avoid consuming the body
            val requestLineBytes = mutableListOf<Byte>()
            var byte: Int

            // Read until we find \r\n
            while (inputStream.read().also { byte = it } != -1) {
                if (byte == '\r'.code) {
                    val nextByte = inputStream.read()
                    if (nextByte == '\n'.code) break
                    else {
                        requestLineBytes.add(byte.toByte())
                        if (nextByte != -1) requestLineBytes.add(nextByte.toByte())
                    }
                } else {
                    requestLineBytes.add(byte.toByte())
                }
            }

            val requestLine = String(requestLineBytes.toByteArray(), Charsets.UTF_8)
            println("Request line: $requestLine")

            val parts = requestLine.split(" ")
            if (parts.size < 2) {
                println("Invalid request line format: $requestLine")
                return null
            }

            val method = parts[0]
            val path = parts[1]
            println("Parsed method: $method, path: $path")

            // Read headers line by line
            val headers = mutableMapOf<String, String>()
            while (true) {
                val headerLineBytes = mutableListOf<Byte>()

                // Read header line
                while (inputStream.read().also { byte = it } != -1) {
                    if (byte == '\r'.code) {
                        val nextByte = inputStream.read()
                        if (nextByte == '\n'.code) break
                        else {
                            headerLineBytes.add(byte.toByte())
                            if (nextByte != -1) headerLineBytes.add(nextByte.toByte())
                        }
                    } else {
                        headerLineBytes.add(byte.toByte())
                    }
                }

                val headerLine = String(headerLineBytes.toByteArray(), Charsets.UTF_8)
                if (headerLine.isEmpty()) break // End of headers

                val colonIndex = headerLine.indexOf(':')
                if (colonIndex > 0) {
                    val key = headerLine.substring(0, colonIndex).trim().lowercase()
                    val value = headerLine.substring(colonIndex + 1).trim()
                    headers[key] = value
                    println("Header: $key = $value")
                }
            }

            val contentLength = headers["content-length"]?.toIntOrNull() ?: 0
            println("Content-Length: $contentLength")

            HttpRequest(method, path, headers, contentLength)

        } catch (e: Exception) {
            println("Error parsing HTTP request: ${e.message}")
            e.printStackTrace()
            null
        }
    }

    private fun handlePostRequest(request: HttpRequest, inputStream: InputStream, outputStream: OutputStream) {
        when {
            request.path == "/upload" -> handleMultiFileUpload(inputStream, outputStream, request)
            request.path == "/" -> handleLegacyUpload(inputStream, outputStream, request)
            else -> sendErrorResponse(outputStream, 404, "Not Found")
        }
    }

    private fun handleOptionsRequest(outputStream: OutputStream) {
        val response = """
            HTTP/1.1 200 OK
            Access-Control-Allow-Origin: *
            Access-Control-Allow-Methods: GET, POST, OPTIONS
            Access-Control-Allow-Headers: Content-Type
            Content-Length: 0

        """.trimIndent().replace("\n", "\r\n")

        outputStream.write(response.toByteArray(Charsets.UTF_8))
        outputStream.flush()
    }

    private fun sendErrorResponse(outputStream: OutputStream, code: Int, message: String) {
        val statusText = when (code) {
            400 -> "Bad Request"
            404 -> "Not Found"
            405 -> "Method Not Allowed"
            500 -> "Internal Server Error"
            else -> "Error"
        }

        val html = """
            <html>
            <head><title>$code $statusText</title></head>
            <body>
                <h1>$code $statusText</h1>
                <p>$message</p>
            </body>
            </html>
        """.trimIndent()

        val response = """
            HTTP/1.1 $code $statusText
            Content-Type: text/html; charset=UTF-8
            Content-Length: ${html.toByteArray(Charsets.UTF_8).size}
            Connection: close

            $html
        """.trimIndent().replace("\n", "\r\n")

        outputStream.write(response.toByteArray(Charsets.UTF_8))
        outputStream.flush()
    }

    private fun handleGetRequest(path: String, output: OutputStream) {
        when (path) {
            "/" -> serveMainPage(output)
            "/files" -> serveFileList(output)
            "/test" -> serveTestPage(output)
            else -> {
                if (path.startsWith("/download/")) {
                    val fileName = path.substring(10)
                    serveFile(fileName, output)
                } else {
                    serve404(output)
                }
            }
        }
    }

    private fun serveTestPage(output: OutputStream) {
        val html = """
            <!DOCTYPE html>
            <html>
            <head>
                <title>Upload Test</title>
                <meta charset="UTF-8">
            </head>
            <body>
                <h1>Simple Upload Test</h1>
                <form action="/" method="post" enctype="multipart/form-data">
                    <p>Select a small text file first to test:</p>
                    <input type="file" name="file" required>
                    <br><br>
                    <button type="submit">Upload</button>
                </form>
                <p><a href="/">← Back to main page</a></p>
            </body>
            </html>
        """.trimIndent()

        serveResponseWithCacheBusting(output, "200 OK", "text/html", html)
    }

    private fun handlePostRequestRobust(inputStream: InputStream, output: OutputStream) {
        println("=== Starting robust POST handler ===")
        try {
            // Read headers byte by byte to avoid BufferedReader issues
            val headers = mutableMapOf<String, String>()
            val headerLines = mutableListOf<String>()

            // Read headers line by line
            var currentLine = StringBuilder()
            var byte: Int
            while (inputStream.read().also { byte = it } != -1) {
                when (byte) {
                    '\r'.code -> {
                        // Check for \n
                        if (inputStream.read() == '\n'.code) {
                            val line = currentLine.toString()
                            if (line.isEmpty()) {
                                // Empty line means end of headers
                                break
                            }
                            headerLines.add(line)
                            currentLine = StringBuilder()
                        }
                    }
                    else -> currentLine.append(byte.toChar())
                }
            }

            // Parse headers
            for (line in headerLines) {
                val colonIndex = line.indexOf(":")
                if (colonIndex > 0) {
                    val key = line.substring(0, colonIndex).trim().lowercase()
                    val value = line.substring(colonIndex + 1).trim()
                    headers[key] = value
                }
            }

            val contentLength = headers["content-length"]?.toIntOrNull() ?: 0
            val contentType = headers["content-type"] ?: ""

            println("Headers parsed:")
            println("Content-Length: $contentLength")
            println("Content-Type: $contentType")
            println("Total headers: ${headers.size}")

            // Debug: print all headers
            println("=== All Headers ===")
            for ((key, value) in headers) {
                println("$key: $value")
            }
            println("=== End Headers ===")

            if (contentLength > 0) {
                if (contentType.contains("multipart/form-data")) {
                    val boundary = contentType.substringAfter("boundary=").trim()
                    if (boundary.isNotEmpty()) {
                        println("Starting real file upload handler with boundary: $boundary")
                        try {
                            // Use the robust file upload handler for all files
                            println("Using robust file upload handler")
                            handleRealFileUpload(inputStream, output, boundary, contentLength)
                        } catch (e: Exception) {
                            println("File upload failed, falling back to simple handler: ${e.message}")
                            // Reset input stream is not possible, so we'll handle the error in the function
                            throw e
                        }
                    } else {
                        println("No boundary found, using simple handler")
                        handleUltraSimpleUpload(inputStream, output, contentLength)
                    }
                } else {
                    println("Not multipart data, using simple handler")
                    handleUltraSimpleUpload(inputStream, output, contentLength)
                }
            } else {
                println("No content length, sending 400")
                serve400(output)
            }
        } catch (e: Exception) {
            println("ERROR in handlePostRequestRobust: ${e.message}")
            e.printStackTrace()
            try {
                serve500(output)
            } catch (responseError: Exception) {
                println("ERROR sending 500 response: ${responseError.message}")
            }
        }
    }

    private fun handleUltraSimpleUpload(
        inputStream: InputStream,
        output: OutputStream,
        contentLength: Int
    ) {
        println("=== Starting ultra-simple upload: $contentLength bytes ===")
        var totalRead = 0
        try {
            // Use smaller buffer to be more conservative
            val buffer = ByteArray(32768) // 32KB buffer
            var consecutiveFailures = 0

            while (totalRead < contentLength && consecutiveFailures < 5) {
                try {
                    val bytesToRead = minOf(buffer.size, contentLength - totalRead)
                    val bytesRead = inputStream.read(buffer, 0, bytesToRead)

                    if (bytesRead == -1) {
                        println("End of stream reached at $totalRead bytes")
                        break
                    }

                    if (bytesRead == 0) {
                        consecutiveFailures++
                        println("No bytes read, failure count: $consecutiveFailures")
                        Thread.sleep(10) // Small delay
                        continue
                    }

                    consecutiveFailures = 0
                    totalRead += bytesRead

                    // Progress logging every 2MB
                    if (totalRead % (2 * 1024 * 1024) == 0) {
                        val progressMB = totalRead / (1024 * 1024)
                        val totalMB = contentLength / (1024 * 1024)
                        println("Upload progress: ${progressMB}MB / ${totalMB}MB")
                    }
                } catch (readException: Exception) {
                    println("Read exception: ${readException.message}")
                    consecutiveFailures++
                    if (consecutiveFailures >= 5) {
                        throw readException
                    }
                }
            }

            println("Data consumption complete: $totalRead bytes read")

            // Create success file
            val timestamp = SimpleDateFormat("yyyyMMdd_HHmmss", Locale.getDefault()).format(Date())
            val fileName = "upload_received_$timestamp.txt"
            val outputFile = File(downloadDir, fileName)

            outputFile.writeText(
                "File upload received successfully!\n\n" +
                "Timestamp: $timestamp\n" +
                "Total bytes received: $totalRead\n" +
                "Expected bytes: $contentLength\n" +
                "Success rate: ${(totalRead.toDouble() / contentLength * 100).toInt()}%\n\n" +
                "This demonstrates the server can receive large uploads without crashing."
            )

            println("Success file created: $fileName")

            // Send response
            val responseHtml = """
                <html>
                <head>
                    <title>Upload Complete</title>
                    <style>
                        body { font-family: Arial, sans-serif; max-width: 600px; margin: 50px auto; padding: 20px; }
                        .success { color: green; font-weight: bold; }
                        .info { background: #f0f0f0; padding: 15px; border-radius: 5px; margin: 15px 0; }
                    </style>
                </head>
                <body>
                    <h2 class="success">✓ Upload Complete!</h2>
                    <div class="info">
                        <p><strong>Success file:</strong> $fileName</p>
                        <p><strong>Data received:</strong> ${formatFileSize(totalRead.toLong())}</p>
                        <p><strong>Expected:</strong> ${formatFileSize(contentLength.toLong())}</p>
                    </div>
                    <p>Upload successfully processed by the server.</p>
                    <p><a href='/'>← Back to main page</a></p>
                </body>
                </html>
            """.trimIndent()

            println("Sending success response...")
            serveResponse(output, "200 OK", "text/html", responseHtml)
            println("=== Upload handler completed successfully ===")

        } catch (e: Exception) {
            println("ERROR in handleUltraSimpleUpload: ${e.message}")
            e.printStackTrace()
            try {
                println("Sending error response...")
                serve500(output)
            } catch (responseError: Exception) {
                println("ERROR sending error response: ${responseError.message}")
            }
        }
    }

    private fun handleRealFileUpload(
        inputStream: InputStream,
        output: OutputStream,
        boundary: String,
        contentLength: Int
    ) {
        println("=== Starting robust file upload: $contentLength bytes ===")
        println("Boundary: '$boundary'")

        var totalRead = 0
        var fileName: String? = null
        var actualFile: File? = null

        try {
            // Use a more efficient buffer size
            val buffer = ByteArray(32768) // 32KB buffer
            val boundaryBytes = "--$boundary".toByteArray(Charsets.UTF_8)
            val endBoundaryBytes = "--$boundary--".toByteArray(Charsets.UTF_8)
            
            // State machine for parsing multipart data
            var state = MultipartState.READING_HEADERS
            val headerBuffer = ByteArrayOutputStream()
            var fileOutputStream: FileOutputStream? = null
            var fileContentSize = 0L
            var previousChunk: ByteArray? = null
            
            // Read data in chunks and process it properly
            while (totalRead < contentLength) {
                val bytesToRead = minOf(buffer.size, contentLength - totalRead)
                val bytesRead = inputStream.read(buffer, 0, bytesToRead)

                if (bytesRead == -1) {
                    println("End of stream reached at $totalRead bytes")
                    break
                }

                totalRead += bytesRead

                when (state) {
                    MultipartState.READING_HEADERS -> {
                        // Collect header data
                        headerBuffer.write(buffer, 0, bytesRead)
                        
                        // Check if we have enough data to find header end
                        val headerData = headerBuffer.toByteArray()
                        val headerEndIndex = findHeaderEnd(headerData)
                        
                        if (headerEndIndex != -1) {
                            // Extract filename from headers
                            fileName = extractFilenameFromHeaders(headerData, headerEndIndex)
                            println("Extracted filename: '$fileName'")
                            
                            // Start file content
                            state = MultipartState.READING_FILE_CONTENT
                            
                            // Create output file
                            val sanitizedFileName = sanitizeFileName(fileName ?: "uploaded_file")
                            actualFile = createUniqueFile(sanitizedFileName)
                            fileOutputStream = FileOutputStream(actualFile)
                            
                            // Write any remaining data after headers as file content
                            val remainingData = headerData.size - headerEndIndex
                            if (remainingData > 0) {
                                fileOutputStream.write(headerData, headerEndIndex, remainingData)
                                fileContentSize += remainingData
                            }
                        }
                    }
                    
                    MultipartState.READING_FILE_CONTENT -> {
                        // Use improved boundary detection
                        val (isBoundary, _, safeWriteSize) = handlePartialBoundary(
                            buffer, bytesRead, previousChunk, boundaryBytes, endBoundaryBytes
                        )
                        
                        if (isBoundary) {
                            // Found a complete boundary
                            if (safeWriteSize > 0) {
                                fileOutputStream?.write(buffer, 0, safeWriteSize)
                                fileContentSize += safeWriteSize
                            }
                            state = MultipartState.COMPLETE
                        } else {
                            // No boundary found, write safe amount
                            if (safeWriteSize > 0) {
                                fileOutputStream?.write(buffer, 0, safeWriteSize)
                                fileContentSize += safeWriteSize
                            }
                        }
                        
                        // Store current chunk for next iteration (for boundary detection)
                        previousChunk = buffer.copyOf(bytesRead)
                    }
                    
                    MultipartState.COMPLETE -> {
                        // Already completed, just consume remaining data
                        break
                    }
                }

                // Progress logging
                if (totalRead % (1024 * 1024) == 0) {
                    val progressMB = totalRead / (1024 * 1024)
                    val totalMB = contentLength / (1024 * 1024)
                    println("Upload progress: ${progressMB}MB / ${totalMB}MB")
                }
            }

            // Close file output stream
            fileOutputStream?.close()

            // Verify file was written correctly
            actualFile?.let { file ->
                if (file.exists() && file.length() == fileContentSize) {
                    println("File written successfully: ${file.name}")
                    println("File size: ${formatFileSize(file.length())}")
                } else {
                    println("WARNING: File size mismatch!")
                    println("Expected: $fileContentSize bytes, Actual: ${file.length()} bytes")
                    throw IOException("File size verification failed")
                }
            }

            // Send success response
            val finalFileName = actualFile?.name ?: "unknown_file"
            val fileSize = actualFile?.length() ?: 0

            val responseHtml = """
                <html>
                <head>
                    <title>Upload Complete</title>
                    <meta charset="UTF-8">
                    <style>
                        body { font-family: Arial, sans-serif; max-width: 600px; margin: 50px auto; padding: 20px; }
                        .success { color: green; font-weight: bold; }
                        .info { background: #f0f0f0; padding: 15px; border-radius: 5px; margin: 15px 0; }
                    </style>
                </head>
                <body>
                    <h2 class="success">✓ File Upload Complete!</h2>
                    <div class="info">
                        <p><strong>Original filename:</strong> ${fileName ?: "Unknown"}</p>
                        <p><strong>Saved as:</strong> $finalFileName</p>
                        <p><strong>File size:</strong> ${formatFileSize(fileSize)}</p>
                        <p><strong>Location:</strong> ${downloadDir.absolutePath}</p>
                    </div>
                    <p>Your file has been successfully uploaded and saved with proper encoding!</p>
                    <p><a href='/'>← Back to main page</a></p>
                </body>
                </html>
            """.trimIndent()

            serveResponse(output, "200 OK", "text/html", responseHtml)
            println("=== Robust file upload completed successfully ===")

        } catch (e: Exception) {
            println("ERROR in handleRealFileUpload: ${e.javaClass.simpleName}: ${e.message}")
            e.printStackTrace()

            try {
                actualFile?.delete() // Clean up partial file
            } catch (cleanupError: Exception) {
                println("Error during cleanup: ${cleanupError.message}")
            }
            
            try {
                val errorHtml = """
                    <html>
                    <head><title>Upload Error</title><meta charset="UTF-8"></head>
                    <body>
                        <h1>Upload Error</h1>
                        <p>Error: ${e.javaClass.simpleName}: ${e.message}</p>
                        <p><a href='/'>← Back to main page</a></p>
                    </body>
                    </html>
                """.trimIndent()
                serveResponse(output, "500 Internal Server Error", "text/html", errorHtml)
            } catch (responseError: Exception) {
                println("ERROR sending error response: ${responseError.message}")
            }
        }
    }

    private fun handleMultiFileUpload(inputStream: InputStream, outputStream: OutputStream, request: HttpRequest) {
        println("=== Starting robust multi-file upload handler ===")
        println("Content-Length: ${request.contentLength}")
        println("Headers: ${request.headers}")

        try {
            val contentType = request.headers["content-type"] ?: ""
            println("Content-Type: $contentType")

            if (request.contentLength > 0 && contentType.contains("multipart/form-data")) {
                val boundary = contentType.substringAfter("boundary=").trim()
                println("Extracted boundary: '$boundary'")

                if (boundary.isNotEmpty()) {
                    handleRobustMultipartUpload(inputStream, outputStream, boundary, request.contentLength)
                } else {
                    println("ERROR: Missing boundary in multipart request")
                    sendErrorResponse(outputStream, 400, "Missing boundary in multipart request")
                }
            } else {
                println("ERROR: Invalid upload request - contentLength: ${request.contentLength}, contentType: '$contentType'")
                sendErrorResponse(outputStream, 400, "Invalid upload request")
            }

        } catch (e: Exception) {
            println("ERROR in handleMultiFileUpload: ${e.message}")
            e.printStackTrace()
            sendErrorResponse(outputStream, 500, "Upload failed: ${e.message}")
        }
    }

    private fun handleLegacyUpload(inputStream: InputStream, outputStream: OutputStream, request: HttpRequest) {
        println("=== Handling legacy upload ===")
        println("Request headers: ${request.headers}")
        println("Content-Length: ${request.contentLength}")

        try {
            val contentType = request.headers["content-type"] ?: ""
            println("Content-Type: '$contentType'")

            if (request.contentLength > 0 && contentType.contains("multipart/form-data")) {
                val boundary = contentType.substringAfter("boundary=").trim()
                println("Extracted boundary: '$boundary'")

                if (boundary.isNotEmpty()) {
                    // Handle legacy upload with HTML response
                    handleLegacyMultipartUpload(inputStream, outputStream, boundary, request.contentLength)
                } else {
                    println("ERROR: Missing boundary in multipart request")
                    sendErrorResponse(outputStream, 400, "Missing boundary in multipart request")
                }
            } else {
                println("ERROR: Invalid upload request - contentLength: ${request.contentLength}, contentType: '$contentType'")
                sendErrorResponse(outputStream, 400, "Invalid upload request")
            }

        } catch (e: Exception) {
            println("ERROR in handleLegacyUpload: ${e.message}")
            e.printStackTrace()
            sendErrorResponse(outputStream, 500, "Upload failed: ${e.message}")
        }
    }

    private fun handleRobustMultipartUpload(
        inputStream: InputStream,
        outputStream: OutputStream,
        boundary: String,
        contentLength: Int
    ) {
        println("=== Processing robust multipart upload with boundary: $boundary ===")

        var totalRead = 0
        var filesUploaded = 0
        var relativePath: String? = null
        var currentFile: FileUploadContext? = null

        try {
            val buffer = ByteArray(65536) // 64KB buffer for better performance
            val boundaryBytes = "--$boundary".toByteArray(Charsets.UTF_8)
            val endBoundaryBytes = "--$boundary--".toByteArray(Charsets.UTF_8)

            var state = MultipartState.READING_HEADERS
            val headerBuffer = ByteArrayOutputStream()
            val dataBuffer = ByteArrayOutputStream()
            var previousChunk: ByteArray? = null

            while (totalRead < contentLength) {
                val bytesToRead = minOf(buffer.size, contentLength - totalRead)
                val bytesRead = inputStream.read(buffer, 0, bytesToRead)

                if (bytesRead == -1) {
                    println("End of stream reached at $totalRead bytes")
                    break
                }

                totalRead += bytesRead

                when (state) {
                    MultipartState.READING_HEADERS -> {
                        headerBuffer.write(buffer, 0, bytesRead)
                        val headerData = headerBuffer.toByteArray()
                        val headerEndIndex = findHeaderEnd(headerData)

                        if (headerEndIndex != -1) {
                            val headerInfo = extractMultipartHeaders(headerData, headerEndIndex)
                            val fieldName = headerInfo["name"]
                            val fileName = headerInfo["filename"]

                            println("Processing field: '$fieldName', filename: '$fileName'")

                            state = MultipartState.READING_FILE_CONTENT

                            if (fileName != null) {
                                // File upload
                                val targetPath = if (relativePath != null) {
                                    createDirectoryStructure(relativePath)
                                    relativePath
                                } else {
                                    fileName
                                }

                                val sanitizedPath = sanitizeFilePath(targetPath)
                                val file = createUniqueFileWithPath(sanitizedPath)
                                println("Creating file: ${file.absolutePath}")
                                println("File parent directory: ${file.parentFile?.absolutePath}")
                                println("Parent directory exists: ${file.parentFile?.exists()}")

                                // Ensure parent directory exists
                                file.parentFile?.let { parentDir ->
                                    if (!parentDir.exists()) {
                                        val created = parentDir.mkdirs()
                                        println("Created parent directory: $created")
                                    }
                                }

                                currentFile = FileUploadContext(file, FileOutputStream(file))
                                println("File created successfully: ${file.absolutePath}")

                                val remainingData = headerData.size - headerEndIndex
                                if (remainingData > 0) {
                                    currentFile.outputStream.write(headerData, headerEndIndex, remainingData)
                                    currentFile.bytesWritten += remainingData
                                }
                            } else {
                                // Form field
                                dataBuffer.reset()
                                val remainingData = headerData.size - headerEndIndex
                                if (remainingData > 0) {
                                    dataBuffer.write(headerData, headerEndIndex, remainingData)
                                }
                                currentFile = null
                            }
                        }
                    }

                    MultipartState.READING_FILE_CONTENT -> {
                        val (isBoundary, _, safeWriteSize) = handlePartialBoundary(
                            buffer, bytesRead, previousChunk, boundaryBytes, endBoundaryBytes
                        )

                        if (safeWriteSize > 0) {
                            if (currentFile != null) {
                                currentFile.outputStream.write(buffer, 0, safeWriteSize)
                                currentFile.bytesWritten += safeWriteSize
                            } else {
                                dataBuffer.write(buffer, 0, safeWriteSize)
                            }
                        }

                        if (isBoundary) {
                            // End of current part
                            if (currentFile != null) {
                                currentFile.outputStream.flush()
                                currentFile.outputStream.close()
                                filesUploaded++
                                println("=== FILE UPLOAD COMPLETED ===")
                                println("File path: ${currentFile.file.absolutePath}")
                                println("File size: ${currentFile.bytesWritten} bytes")
                                println("File exists after close: ${currentFile.file.exists()}")
                                println("File length on disk: ${currentFile.file.length()}")
                                println("File readable: ${currentFile.file.canRead()}")
                                println("================================")
                                currentFile = null
                            } else {
                                // Process form field data
                                val fieldData = dataBuffer.toString("UTF-8").trim()
                                if (fieldData.isNotEmpty()) {
                                    relativePath = fieldData
                                    println("Extracted relativePath: '$relativePath'")
                                }
                            }

                            // Reset for next part
                            state = MultipartState.READING_HEADERS
                            headerBuffer.reset()
                        }

                        previousChunk = buffer.copyOf(bytesRead)
                    }

                    MultipartState.COMPLETE -> break
                }
            }

            // Clean up any remaining file
            if (currentFile != null) {
                println("=== CLEANING UP FINAL FILE ===")
                currentFile.outputStream.flush()
                currentFile.outputStream.close()
                println("Final file path: ${currentFile.file.absolutePath}")
                println("Final file exists: ${currentFile.file.exists()}")
                println("Final file size: ${currentFile.file.length()}")
                filesUploaded++
            }

            println("=== UPLOAD SUMMARY ===")
            println("Total files uploaded: $filesUploaded")
            println("Download directory: ${downloadDir.absolutePath}")
            println("Directory exists: ${downloadDir.exists()}")
            println("Directory contents:")
            downloadDir.listFiles()?.forEach { file ->
                println("  - ${file.name} (${file.length()} bytes)")
            }
            println("=====================")

            // Send success response
            val responseJson = """{"status": "success", "message": "Successfully uploaded $filesUploaded files", "filesUploaded": $filesUploaded}"""
            sendJsonResponse(outputStream, 200, responseJson)

        } catch (e: Exception) {
            println("ERROR in handleRobustMultipartUpload: ${e.message}")
            e.printStackTrace()

            // Clean up any partial file
            try {
                currentFile?.outputStream?.close()
                currentFile?.file?.delete()
            } catch (cleanupError: Exception) {
                println("Error during cleanup: ${cleanupError.message}")
            }

            val errorJson = """{"status": "error", "message": "Upload failed: ${e.message}"}"""
            sendJsonResponse(outputStream, 500, errorJson)
        }
    }

    private fun handleLegacyMultipartUpload(
        inputStream: InputStream,
        outputStream: OutputStream,
        boundary: String,
        contentLength: Int
    ) {
        println("=== Processing legacy multipart upload ===")

        try {
            // Use the old working upload logic from handlePostRequestRobust
            var totalRead = 0
            var fileName: String? = null
            var actualFile: File? = null

            val buffer = ByteArray(32768)
            val boundaryBytes = "--$boundary".toByteArray(Charsets.UTF_8)
            val endBoundaryBytes = "--$boundary--".toByteArray(Charsets.UTF_8)

            var state = MultipartState.READING_HEADERS
            val headerBuffer = ByteArrayOutputStream()
            var fileOutputStream: FileOutputStream? = null
            var fileContentSize = 0L
            var previousChunk: ByteArray? = null

            while (totalRead < contentLength) {
                val bytesToRead = minOf(buffer.size, contentLength - totalRead)
                val bytesRead = inputStream.read(buffer, 0, bytesToRead)

                if (bytesRead == -1) {
                    println("End of stream reached at $totalRead bytes")
                    break
                }

                totalRead += bytesRead

                when (state) {
                    MultipartState.READING_HEADERS -> {
                        headerBuffer.write(buffer, 0, bytesRead)
                        val headerData = headerBuffer.toByteArray()
                        val headerEndIndex = findHeaderEnd(headerData)

                        if (headerEndIndex != -1) {
                            val headerInfo = extractMultipartHeaders(headerData, headerEndIndex)
                            fileName = headerInfo["filename"]

                            println("Legacy upload - filename: '$fileName'")

                            state = MultipartState.READING_FILE_CONTENT

                            if (fileName != null) {
                                val sanitizedFileName = sanitizeFileName(fileName)
                                actualFile = createUniqueFile(sanitizedFileName)
                                fileOutputStream = FileOutputStream(actualFile)

                                val remainingData = headerData.size - headerEndIndex
                                if (remainingData > 0) {
                                    fileOutputStream.write(headerData, headerEndIndex, remainingData)
                                    fileContentSize += remainingData
                                }
                            }
                        }
                    }

                    MultipartState.READING_FILE_CONTENT -> {
                        val (isBoundary, _, safeWriteSize) = handlePartialBoundary(
                            buffer, bytesRead, previousChunk, boundaryBytes, endBoundaryBytes
                        )

                        if (isBoundary) {
                            if (safeWriteSize > 0) {
                                fileOutputStream?.write(buffer, 0, safeWriteSize)
                                fileContentSize += safeWriteSize
                            }

                            fileOutputStream?.close()
                            fileOutputStream = null

                            println("Legacy file upload completed: ${actualFile?.name} ($fileContentSize bytes)")
                            break
                        } else {
                            if (safeWriteSize > 0) {
                                fileOutputStream?.write(buffer, 0, safeWriteSize)
                                fileContentSize += safeWriteSize
                            }
                        }

                        previousChunk = buffer.copyOf(bytesRead)
                    }

                    MultipartState.COMPLETE -> break
                }
            }

            fileOutputStream?.close()

            // Send HTML response for legacy upload
            val html = """
                <html>
                <head>
                    <title>Upload Complete</title>
                    <meta charset="UTF-8">
                    <meta http-equiv="refresh" content="3;url=/">
                </head>
                <body>
                    <h1>Upload Successful!</h1>
                    <p>File "${fileName ?: "unknown"}" uploaded successfully.</p>
                    <p>File size: $fileContentSize bytes</p>
                    <p>Redirecting to main page in 3 seconds...</p>
                    <a href="/">← Back to main page</a>
                </body>
                </html>
            """.trimIndent()

            val response = """
                HTTP/1.1 200 OK
                Content-Type: text/html; charset=UTF-8
                Content-Length: ${html.toByteArray(Charsets.UTF_8).size}
                Connection: close

                $html
            """.trimIndent().replace("\n", "\r\n")

            outputStream.write(response.toByteArray(Charsets.UTF_8))
            outputStream.flush()

        } catch (e: Exception) {
            println("ERROR in handleLegacyMultipartUpload: ${e.message}")
            e.printStackTrace()

            actualFile?.delete()

            val errorHtml = """
                <html>
                <head><title>Upload Error</title></head>
                <body>
                    <h1>Upload Failed</h1>
                    <p>Error: ${e.message}</p>
                    <a href="/">← Back to main page</a>
                </body>
                </html>
            """.trimIndent()

            val errorResponse = """
                HTTP/1.1 500 Internal Server Error
                Content-Type: text/html; charset=UTF-8
                Content-Length: ${errorHtml.toByteArray(Charsets.UTF_8).size}
                Connection: close

                $errorHtml
            """.trimIndent().replace("\n", "\r\n")

            outputStream.write(errorResponse.toByteArray(Charsets.UTF_8))
            outputStream.flush()
        }
    }

    private data class FileUploadContext(
        val file: File,
        val outputStream: FileOutputStream,
        var bytesWritten: Long = 0
    )

    private fun sendJsonResponse(outputStream: OutputStream, statusCode: Int, json: String) {
        val statusText = when (statusCode) {
            200 -> "OK"
            400 -> "Bad Request"
            500 -> "Internal Server Error"
            else -> "Unknown"
        }

        val response = """
            HTTP/1.1 $statusCode $statusText
            Content-Type: application/json; charset=UTF-8
            Content-Length: ${json.toByteArray(Charsets.UTF_8).size}
            Access-Control-Allow-Origin: *
            Connection: close

            $json
        """.trimIndent().replace("\n", "\r\n")

        outputStream.write(response.toByteArray(Charsets.UTF_8))
        outputStream.flush()
    }

    private enum class MultipartState {
        READING_HEADERS,
        READING_FILE_CONTENT,
        COMPLETE
    }

    private fun findHeaderEnd(data: ByteArray): Int {
        // Look for \r\n\r\n or \n\n patterns
        val patterns = listOf(
            byteArrayOf('\r'.code.toByte(), '\n'.code.toByte(), '\r'.code.toByte(), '\n'.code.toByte()),
            byteArrayOf('\n'.code.toByte(), '\n'.code.toByte())
        )
        
        for (pattern in patterns) {
            for (i in 0..data.size - pattern.size) {
                var match = true
                for (j in pattern.indices) {
                    if (data[i + j] != pattern[j]) {
                        match = false
                        break
                    }
                }
                if (match) {
                    return i + pattern.size
                }
            }
        }
        return -1
    }

    private fun extractFilenameFromHeaders(headerData: ByteArray, headerEndIndex: Int): String? {
        // Convert only the header portion to string for filename extraction
        val headerString = String(headerData, 0, headerEndIndex, Charsets.UTF_8)
        
        // Use a more robust filename extraction approach
        val filenamePatterns = listOf(
            // RFC 5987 encoded filename (most reliable for non-ASCII)
            Regex("""filename\*\s*=\s*UTF-8''([^;\r\n]+)""", RegexOption.IGNORE_CASE),
            // Standard quoted filename
            Regex("""filename\s*=\s*"([^"]+)"""", RegexOption.IGNORE_CASE),
            // Unquoted filename
            Regex("""filename\s*=\s*([^;\s\r\n]+)""", RegexOption.IGNORE_CASE)
        )
        
        for (pattern in filenamePatterns) {
            val match = pattern.find(headerString)
            if (match != null) {
                var filename = match.groupValues[1].trim()
                
                // Handle URL decoding for RFC 5987 format
                if (pattern == filenamePatterns[0]) {
                    try {
                        filename = URLDecoder.decode(filename, "UTF-8")
                    } catch (e: Exception) {
                        println("Warning: Could not URL decode filename '$filename': ${e.message}")
                    }
                }
                
                return filename
            }
        }
        
        return null
    }

    private fun findBoundaryInChunk(chunk: ByteArray, chunkSize: Int, boundaryBytes: ByteArray): Int {
        for (i in 0..chunkSize - boundaryBytes.size) {
            var match = true
            for (j in boundaryBytes.indices) {
                if (chunk[i + j] != boundaryBytes[j]) {
                    match = false
                    break
                }
            }
            if (match) {
                return i
            }
        }
        return -1
    }

    private fun findBoundaryAcrossChunks(
        currentChunk: ByteArray, 
        currentChunkSize: Int, 
        previousChunk: ByteArray?, 
        boundaryBytes: ByteArray
    ): Int {
        // First check if boundary starts in previous chunk and continues in current
        if (previousChunk != null) {
            val overlapSize = minOf(boundaryBytes.size - 1, previousChunk.size)
            for (overlap in 1..overlapSize) {
                var match = true
                // Check overlap part in previous chunk
                for (i in 0 until overlap) {
                    if (previousChunk[previousChunk.size - overlap + i] != boundaryBytes[i]) {
                        match = false
                        break
                    }
                }
                if (match) {
                    // Check remaining part in current chunk
                    for (i in overlap until boundaryBytes.size) {
                        if (i - overlap >= currentChunkSize || currentChunk[i - overlap] != boundaryBytes[i]) {
                            match = false
                            break
                        }
                    }
                    if (match) {
                        return -(overlap) // Negative indicates boundary starts in previous chunk
                    }
                }
            }
        }
        
        // Check if boundary is fully within current chunk
        return findBoundaryInChunk(currentChunk, currentChunkSize, boundaryBytes)
    }

    private fun isPartialBoundaryAtEnd(chunk: ByteArray, chunkSize: Int, boundaryBytes: ByteArray): Boolean {
        // Check if the end of the chunk could be the start of a boundary
        val maxPartialSize = minOf(boundaryBytes.size - 1, chunkSize)
        for (partialSize in 1..maxPartialSize) {
            var match = true
            for (i in 0 until partialSize) {
                if (chunk[chunkSize - partialSize + i] != boundaryBytes[i]) {
                    match = false
                    break
                }
            }
            if (match) {
                return true
            }
        }
        return false
    }

    private fun handlePartialBoundary(
        currentChunk: ByteArray,
        currentChunkSize: Int,
        previousChunk: ByteArray?,
        boundaryBytes: ByteArray,
        endBoundaryBytes: ByteArray
    ): Triple<Boolean, Int, Int> {
        // Returns (isBoundary, boundaryType, safeWriteSize)
        // boundaryType: 1 = regular boundary, 2 = end boundary
        
        // Check for complete boundaries first
        val endBoundaryIndex = findBoundaryAcrossChunks(currentChunk, currentChunkSize, previousChunk, endBoundaryBytes)
        if (endBoundaryIndex != -1) {
            return Triple(true, 2, if (endBoundaryIndex > 0) endBoundaryIndex else 0)
        }
        
        val boundaryIndex = findBoundaryAcrossChunks(currentChunk, currentChunkSize, previousChunk, boundaryBytes)
        if (boundaryIndex != -1) {
            return Triple(true, 1, if (boundaryIndex > 0) boundaryIndex else 0)
        }
        
        // Check for partial boundaries
        val hasPartialEndBoundary = isPartialBoundaryAtEnd(currentChunk, currentChunkSize, endBoundaryBytes)
        val hasPartialBoundary = isPartialBoundaryAtEnd(currentChunk, currentChunkSize, boundaryBytes)
        
        if (hasPartialEndBoundary || hasPartialBoundary) {
            val maxBoundarySize = maxOf(boundaryBytes.size, endBoundaryBytes.size)
            val safeWriteSize = currentChunkSize - maxBoundarySize + 1
            return Triple(false, 0, maxOf(0, safeWriteSize))
        }
        
        return Triple(false, 0, currentChunkSize)
    }

    private fun createUniqueFile(fileName: String): File {
        var file = File(downloadDir, fileName)
        var counter = 1
        
        while (file.exists()) {
            val nameWithoutExt = fileName.substringBeforeLast(".", fileName)
            val extension = if (fileName.contains(".")) ".${fileName.substringAfterLast(".")}" else ""
            file = File(downloadDir, "${nameWithoutExt}_$counter$extension")
            counter++
        }
        
        return file
    }

    private fun serveMainPage(output: OutputStream) {
        val html = """
            <!DOCTYPE html>
            <html>
            <head>
                <title>WiFi File Transfer</title>
                <meta charset="UTF-8">
                <meta name="viewport" content="width=device-width, initial-scale=1">
                <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bulma@0.9.4/css/bulma.min.css">
                <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
                <style>
                    .upload-progress { margin-top: 1rem; }
                    .file-item { margin-bottom: 0.5rem; }
                    .hero { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); }
                </style>
            </head>
            <body>
                <section class="hero is-primary">
                    <div class="hero-body">
                        <div class="container">
                            <h1 class="title">
                                <i class="fas fa-wifi"></i> WiFi File Transfer
                            </h1>
                            <p class="subtitle">
                                Transfer files wirelessly between your devices
                            </p>
                        </div>
                    </div>
                </section>

                <div class="container" style="margin-top: 2rem;">
                    <div class="columns">
                        <div class="column is-half">
                            <div class="box">
                                <h2 class="title is-4">
                                    <i class="fas fa-upload"></i> Upload Files/Folders to Phone
                                </h2>

                                <!-- Upload Mode Selection -->
                                <div class="field">
                                    <div class="control">
                                        <div class="tabs is-boxed">
                                            <ul>
                                                <li class="is-active" id="filesTab">
                                                    <a onclick="switchUploadMode('files')">
                                                        <span class="icon is-small"><i class="fas fa-file"></i></span>
                                                        <span>Files</span>
                                                    </a>
                                                </li>
                                                <li id="folderTab">
                                                    <a onclick="switchUploadMode('folder')">
                                                        <span class="icon is-small"><i class="fas fa-folder"></i></span>
                                                        <span>Folder</span>
                                                    </a>
                                                </li>
                                            </ul>
                                        </div>
                                    </div>
                                </div>

                                <!-- File Upload Section -->
                                <div id="filesUploadSection">
                                    <div class="field">
                                        <div class="file is-boxed is-large">
                                            <label class="file-label">
                                                <input class="file-input" type="file" multiple id="fileInput">
                                                <span class="file-cta">
                                                    <span class="file-icon">
                                                        <i class="fas fa-upload"></i>
                                                    </span>
                                                    <span class="file-label" id="fileLabel">
                                                        Choose files...
                                                    </span>
                                                </span>
                                            </label>
                                        </div>
                                        <p class="help">Select multiple files to upload (Ctrl+Click or Cmd+Click)</p>
                                    </div>
                                </div>

                                <!-- Folder Upload Section -->
                                <div id="folderUploadSection" style="display: none;">
                                    <div class="field">
                                        <div class="file is-boxed is-large">
                                            <label class="file-label">
                                                <input class="file-input" type="file" webkitdirectory directory id="folderInput">
                                                <span class="file-cta">
                                                    <span class="file-icon">
                                                        <i class="fas fa-folder-open"></i>
                                                    </span>
                                                    <span class="file-label" id="folderLabel">
                                                        Choose folder...
                                                    </span>
                                                </span>
                                            </label>
                                        </div>
                                        <p class="help">Select a folder to upload (folder structure will be preserved)</p>
                                    </div>
                                </div>

                                <!-- Selected Files Display -->
                                <div id="selectedFilesSection" style="display: none;">
                                    <div class="field">
                                        <label class="label">Selected Files:</label>
                                        <div class="box" style="max-height: 200px; overflow-y: auto;">
                                            <div id="selectedFilesList"></div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Upload Button -->
                                <div class="field">
                                    <div class="control">
                                        <button class="button is-primary is-large" onclick="startUpload()" id="uploadBtn" disabled>
                                            <span class="icon">
                                                <i class="fas fa-cloud-upload-alt"></i>
                                            </span>
                                            <span>Upload Files</span>
                                        </button>
                                    </div>
                                </div>

                                <!-- Progress Section -->
                                <div id="uploadProgressSection" style="display: none;">
                                    <div class="field">
                                        <label class="label">Overall Progress:</label>
                                        <progress class="progress is-primary" id="overallProgress" value="0" max="100">0%</progress>
                                        <p id="overallStatus">Ready to upload...</p>
                                    </div>

                                    <div class="field">
                                        <label class="label">Individual Files:</label>
                                        <div class="box" style="max-height: 300px; overflow-y: auto;">
                                            <div id="individualProgress"></div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="column is-half">
                            <div class="box">
                                <h2 class="title is-4">
                                    <i class="fas fa-download"></i> Download Files from Phone
                                </h2>
                                <div id="fileList" class="content">
                                    <div class="has-text-centered">
                                        <i class="fas fa-spinner fa-spin fa-2x"></i>
                                        <p>Loading files...</p>
                                    </div>
                                </div>
                                <div class="field">
                                    <div class="control">
                                        <button class="button is-info" onclick="refreshFileList()">
                                            <span class="icon">
                                                <i class="fas fa-sync-alt"></i>
                                            </span>
                                            <span>Refresh</span>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="container" style="margin-top: 1rem;">
                    <div class="has-text-centered">
                        <a href="/test" class="button is-light">
                            <span class="icon">
                                <i class="fas fa-flask"></i>
                            </span>
                            <span>Simple Upload Test</span>
                        </a>
                    </div>
                </div>
                
                <script>
                    function refreshFileList() {
                        const fileList = document.getElementById('fileList');
                        fileList.innerHTML = `
                            <div class="has-text-centered">
                                <i class="fas fa-spinner fa-spin fa-2x"></i>
                                <p>Loading files...</p>
                            </div>
                        `;

                        fetch('/files')
                            .then(response => response.json())
                            .then(files => {
                                if (files.length === 0) {
                                    fileList.innerHTML = `
                                        <div class="has-text-centered">
                                            <i class="fas fa-folder-open fa-3x has-text-grey-light"></i>
                                            <p class="has-text-grey">No files available for download.</p>
                                        </div>
                                    `;
                                } else {
                                    fileList.innerHTML = files.map(file => `
                                        <div class="box file-item">
                                            <div class="media">
                                                <div class="media-left">
                                                    <span class="icon is-large has-text-primary">
                                                        <i class="fas fa-file fa-2x"></i>
                                                    </span>
                                                </div>
                                                <div class="media-content">
                                                    <p class="title is-6">${'$'}{file.name}</p>
                                                    <p class="subtitle is-7 has-text-grey">${'$'}{file.size}</p>
                                                </div>
                                                <div class="media-right">
                                                    <a href="/download/${'$'}{file.encodedName}"
                                                       class="button is-primary is-outlined">
                                                        <span class="icon">
                                                            <i class="fas fa-download"></i>
                                                        </span>
                                                        <span>Download</span>
                                                    </a>
                                                </div>
                                            </div>
                                        </div>
                                    `).join('');
                                }
                            })
                            .catch(err => {
                                fileList.innerHTML = `
                                    <div class="notification is-danger">
                                        <i class="fas fa-exclamation-triangle"></i>
                                        Error loading files. Please try again.
                                    </div>
                                `;
                            });
                    }

                    // Update file input label when file is selected
                    document.getElementById('fileInput').addEventListener('change', function(e) {
                        const fileLabel = document.getElementById('fileLabel');
                        const files = e.target.files;
                        if (files.length > 0) {
                            fileLabel.textContent = files[0].name;
                        } else {
                            fileLabel.textContent = 'Choose file...';
                        }
                    });

                    // Global variables for upload management
                    let selectedFiles = [];
                    let uploadMode = 'files';
                    let currentUploads = [];
                    let totalUploadSize = 0;
                    let uploadedSize = 0;

                    // Switch between files and folder upload modes
                    function switchUploadMode(mode) {
                        uploadMode = mode;
                        const filesTab = document.getElementById('filesTab');
                        const folderTab = document.getElementById('folderTab');
                        const filesSection = document.getElementById('filesUploadSection');
                        const folderSection = document.getElementById('folderUploadSection');

                        if (mode === 'files') {
                            filesTab.classList.add('is-active');
                            folderTab.classList.remove('is-active');
                            filesSection.style.display = 'block';
                            folderSection.style.display = 'none';
                        } else {
                            folderTab.classList.add('is-active');
                            filesTab.classList.remove('is-active');
                            filesSection.style.display = 'none';
                            folderSection.style.display = 'block';
                        }

                        // Clear selections when switching modes
                        selectedFiles = [];
                        updateSelectedFilesDisplay();
                        updateUploadButton();
                    }

                    // Handle file selection
                    document.getElementById('fileInput').addEventListener('change', function(e) {
                        selectedFiles = Array.from(e.target.files);
                        updateSelectedFilesDisplay();
                        updateUploadButton();
                    });

                    // Handle folder selection
                    document.getElementById('folderInput').addEventListener('change', function(e) {
                        selectedFiles = Array.from(e.target.files);
                        updateSelectedFilesDisplay();
                        updateUploadButton();
                    });

                    // Update the display of selected files
                    function updateSelectedFilesDisplay() {
                        const section = document.getElementById('selectedFilesSection');
                        const list = document.getElementById('selectedFilesList');

                        if (selectedFiles.length === 0) {
                            section.style.display = 'none';
                            return;
                        }

                        section.style.display = 'block';

                        let totalSize = 0;
                        let html = '';

                        selectedFiles.forEach((file, index) => {
                            totalSize += file.size;
                            const sizeStr = formatFileSize(file.size);
                            const path = file.webkitRelativePath || file.name;

                            html += `
                                <div class="media">
                                    <div class="media-left">
                                        <span class="icon">
                                            <i class="fas fa-${'$'}{file.webkitRelativePath ? 'folder' : 'file'}"></i>
                                        </span>
                                    </div>
                                    <div class="media-content">
                                        <p class="is-size-7"><strong>${'$'}{path}</strong></p>
                                        <p class="is-size-7 has-text-grey">${'$'}{sizeStr}</p>
                                    </div>
                                </div>
                            `;
                        });

                        html += `<hr><p class="has-text-weight-bold">Total: ${'$'}{selectedFiles.length} files, ${'$'}{formatFileSize(totalSize)}</p>`;
                        list.innerHTML = html;
                    }

                    // Update upload button state
                    function updateUploadButton() {
                        const btn = document.getElementById('uploadBtn');
                        if (selectedFiles.length > 0) {
                            btn.disabled = false;
                            btn.querySelector('span:last-child').textContent = `Upload ${'$'}{selectedFiles.length} file${'$'}{selectedFiles.length > 1 ? 's' : ''}`;
                        } else {
                            btn.disabled = true;
                            btn.querySelector('span:last-child').textContent = 'Upload Files';
                        }
                    }

                    // Format file size helper
                    function formatFileSize(bytes) {
                        if (bytes === 0) return '0 B';
                        const k = 1024;
                        const sizes = ['B', 'KB', 'MB', 'GB'];
                        const i = Math.floor(Math.log(bytes) / Math.log(k));
                        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
                    }

                    // Start the upload process
                    async function startUpload() {
                        if (selectedFiles.length === 0) return;

                        // Show progress section
                        document.getElementById('uploadProgressSection').style.display = 'block';
                        document.getElementById('uploadBtn').disabled = true;

                        // Calculate total size
                        totalUploadSize = selectedFiles.reduce((sum, file) => sum + file.size, 0);
                        uploadedSize = 0;

                        // Initialize progress displays
                        updateOverallProgress();
                        initializeIndividualProgress();

                        // Upload files sequentially to avoid overwhelming the server
                        for (let i = 0; i < selectedFiles.length; i++) {
                            const file = selectedFiles[i];
                            await uploadSingleFile(file, i);
                        }

                        // Upload complete
                        document.getElementById('overallStatus').textContent = 'Upload completed successfully!';
                        document.getElementById('uploadBtn').disabled = false;
                        document.getElementById('uploadBtn').querySelector('span:last-child').textContent = 'Upload More Files';

                        // Refresh file list
                        setTimeout(refreshFileList, 1000);
                    }

                    // Upload a single file with progress tracking
                    function uploadSingleFile(file, index) {
                        return new Promise((resolve, reject) => {
                            const formData = new FormData();
                            formData.append('file', file);

                            // Add relative path for folder uploads
                            if (file.webkitRelativePath) {
                                formData.append('relativePath', file.webkitRelativePath);
                            }

                            const xhr = new XMLHttpRequest();

                            // Track upload progress
                            xhr.upload.addEventListener('progress', function(e) {
                                if (e.lengthComputable) {
                                    const percentComplete = (e.loaded / e.total) * 100;
                                    updateFileProgress(index, percentComplete, e.loaded, e.total);

                                    // Update overall progress
                                    const previouslyUploaded = selectedFiles.slice(0, index).reduce((sum, f) => sum + f.size, 0);
                                    const currentFileProgress = e.loaded;
                                    const totalProgress = previouslyUploaded + currentFileProgress;
                                    const overallPercent = (totalProgress / totalUploadSize) * 100;

                                    document.getElementById('overallProgress').value = overallPercent;
                                    document.getElementById('overallStatus').textContent =
                                        `Uploading file ${'$'}{index + 1} of ${'$'}{selectedFiles.length}: ${'$'}{file.name}`;
                                }
                            });

                            xhr.addEventListener('load', function() {
                                if (xhr.status === 200) {
                                    updateFileProgress(index, 100, file.size, file.size, 'completed');
                                    uploadedSize += file.size;
                                    resolve();
                                } else {
                                    updateFileProgress(index, 0, 0, file.size, 'error');
                                    reject(new Error(`Upload failed: ${'$'}{xhr.status}`));
                                }
                            });

                            xhr.addEventListener('error', function() {
                                updateFileProgress(index, 0, 0, file.size, 'error');
                                reject(new Error('Upload failed'));
                            });

                            xhr.open('POST', '/upload');
                            xhr.send(formData);
                        });
                    }

                    // Initialize individual progress displays
                    function initializeIndividualProgress() {
                        const container = document.getElementById('individualProgress');
                        let html = '';

                        selectedFiles.forEach((file, index) => {
                            const path = file.webkitRelativePath || file.name;
                            html += `
                                <div class="field" id="fileProgress${'$'}{index}">
                                    <label class="label is-size-7">${'$'}{path}</label>
                                    <progress class="progress is-small" id="progress${'$'}{index}" value="0" max="100">0%</progress>
                                    <p class="is-size-7" id="status${'$'}{index}">Waiting...</p>
                                </div>
                            `;
                        });

                        container.innerHTML = html;
                    }

                    // Update individual file progress
                    function updateFileProgress(index, percent, loaded, total, status = 'uploading') {
                        const progressBar = document.getElementById(`progress${'$'}{index}`);
                        const statusText = document.getElementById(`status${'$'}{index}`);

                        if (progressBar) {
                            progressBar.value = percent;

                            if (status === 'completed') {
                                progressBar.className = 'progress is-small is-success';
                                statusText.textContent = 'Completed';
                                statusText.className = 'is-size-7 has-text-success';
                            } else if (status === 'error') {
                                progressBar.className = 'progress is-small is-danger';
                                statusText.textContent = 'Error';
                                statusText.className = 'is-size-7 has-text-danger';
                            } else {
                                progressBar.className = 'progress is-small is-primary';
                                statusText.textContent = `${'$'}{formatFileSize(loaded)} / ${'$'}{formatFileSize(total)} (${'$'}{Math.round(percent)}%)`;
                                statusText.className = 'is-size-7';
                            }
                        }
                    }

                    // Update overall progress
                    function updateOverallProgress() {
                        const progressBar = document.getElementById('overallProgress');
                        const statusText = document.getElementById('overallStatus');

                        if (totalUploadSize > 0) {
                            const percent = (uploadedSize / totalUploadSize) * 100;
                            progressBar.value = percent;
                            statusText.textContent = `${'$'}{formatFileSize(uploadedSize)} / ${'$'}{formatFileSize(totalUploadSize)} (${'$'}{Math.round(percent)}%)`;
                        }
                    }

                    // Load file list on page load
                    refreshFileList();
                </script>
            </body>
            </html>
        """.trimIndent()

        serveResponseWithCacheBusting(output, "200 OK", "text/html", html)
    }

    private fun serveFileList(output: OutputStream) {
        val files = downloadDir.listFiles()?.map { file ->
            mapOf(
                "name" to file.name,
                "encodedName" to URLEncoder.encode(file.name, "UTF-8"),
                "size" to formatFileSize(file.length()),
                "readable" to file.canRead().toString()
            )
        } ?: emptyList()

        val json = files.joinToString(",", "[", "]") { file ->
            """{"name":"${escapeJsonString(file["name"] as String)}","encodedName":"${escapeJsonString(file["encodedName"] as String)}","size":"${escapeJsonString(file["size"] as String)}","readable":"${file["readable"]}"}"""
        }

        println("File list JSON: $json")
        serveResponse(output, "200 OK", "application/json; charset=UTF-8", json)
    }

    private fun serveFile(fileName: String, output: OutputStream) {
        println("=== Serving file request ===")
        println("Original filename: '$fileName'")
        
        // URL decode the filename to handle non-ASCII characters
        val decodedFileName = try {
            URLDecoder.decode(fileName, "UTF-8")
        } catch (e: Exception) {
            println("Error decoding filename '$fileName': ${e.message}")
            fileName
        }
        
        println("Decoded filename: '$decodedFileName'")
        
        val file = File(downloadDir, decodedFileName)
        println("File path: ${file.absolutePath}")
        println("File exists: ${file.exists()}")
        println("Is file: ${file.isFile}")
        println("File can read: ${file.canRead()}")
        println("File length: ${file.length()}")
        
        if (file.exists() && file.isFile) {
            try {
                // Check if file is readable
                if (!file.canRead()) {
                    println("ERROR: File exists but is not readable!")
                    serve500(output)
                    return
                }
                
                // Check if file has content
                if (file.length() == 0L) {
                    println("WARNING: File is empty (0 bytes)")
                }
                
                // Properly encode filename for Content-Disposition header
                val encodedFileName = try {
                    URLEncoder.encode(decodedFileName, "UTF-8").replace("+", "%20")
                } catch (e: Exception) {
                    println("Error encoding filename '$decodedFileName': ${e.message}")
                    decodedFileName
                }
                
                println("Encoded filename for header: '$encodedFileName'")
                
                val headers = "HTTP/1.1 200 OK\r\n" +
                        "Content-Type: application/octet-stream\r\n" +
                        "Content-Disposition: attachment; filename=\"$decodedFileName\"; filename*=UTF-8''$encodedFileName\r\n" +
                        "Content-Length: ${file.length()}\r\n" +
                        "\r\n"

                println("Sending headers: $headers")
                output.write(headers.toByteArray(Charsets.UTF_8))

                // Read and send file content
                var bytesSent = 0L
                FileInputStream(file).use { fileInput ->
                    val buffer = ByteArray(8192)
                    var bytesRead: Int
                    while (fileInput.read(buffer).also { bytesRead = it } != -1) {
                        output.write(buffer, 0, bytesRead)
                        bytesSent += bytesRead
                    }
                }
                
                println("File served successfully: $bytesSent bytes sent")
                output.flush()
            } catch (e: Exception) {
                println("ERROR serving file '$decodedFileName': ${e.message}")
                e.printStackTrace()
                try {
                    serve500(output)
                } catch (responseError: Exception) {
                    println("ERROR sending 500 response: ${responseError.message}")
                }
            }
        } else {
            println("File not found: '$decodedFileName' (exists: ${file.exists()}, isFile: ${file.isFile})")
            
            // List files in directory for debugging
            try {
                val files = downloadDir.listFiles()
                println("Files in download directory:")
                files?.forEach { f ->
                    println("  - ${f.name} (${f.length()} bytes, readable: ${f.canRead()})")
                }
            } catch (e: Exception) {
                println("Error listing directory contents: ${e.message}")
            }
            
            serve404(output)
        }
    }

    private fun serveResponse(output: OutputStream, status: String, contentType: String, content: String) {
        try {
            val contentBytes = content.toByteArray(Charsets.UTF_8)
            val response = "HTTP/1.1 $status\r\n" +
                    "Content-Type: $contentType\r\n" +
                    "Content-Length: ${contentBytes.size}\r\n" +
                    "Connection: close\r\n" +
                    "\r\n" +
                    content

            output.write(response.toByteArray(Charsets.UTF_8))
            output.flush()
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    private fun serveResponseWithCacheBusting(output: OutputStream, status: String, contentType: String, content: String) {
        try {
            val contentBytes = content.toByteArray(Charsets.UTF_8)
            val response = "HTTP/1.1 $status\r\n" +
                    "Content-Type: $contentType\r\n" +
                    "Content-Length: ${contentBytes.size}\r\n" +
                    "Cache-Control: no-cache, no-store, must-revalidate\r\n" +
                    "Pragma: no-cache\r\n" +
                    "Expires: 0\r\n" +
                    "Connection: close\r\n" +
                    "\r\n" +
                    content

            output.write(response.toByteArray(Charsets.UTF_8))
            output.flush()
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    private fun serve404(output: OutputStream) {
        try {
            serveResponse(output, "404 Not Found", "text/html",
                "<html><body><h1>404 - File Not Found</h1></body></html>")
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    private fun serve400(output: OutputStream) {
        try {
            serveResponse(output, "400 Bad Request", "text/html",
                "<html><body><h1>400 - Bad Request</h1></body></html>")
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    private fun serve500(output: OutputStream) {
        try {
            serveResponse(output, "500 Internal Server Error", "text/html",
                "<html><body><h1>500 - Internal Server Error</h1></body></html>")
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    private fun sanitizeFileName(fileName: String): String {
        if (fileName.isBlank()) {
            return "unnamed_file"
        }
        
        // Remove or replace problematic characters while preserving Unicode
        var sanitized = fileName.trim()
        
        // Replace only truly invalid file system characters
        // Keep Unicode characters, only remove Windows/Unix reserved chars
        val invalidChars = charArrayOf('<', '>', ':', '"', '|', '?', '*', '\\', '/')
        for (char in invalidChars) {
            sanitized = sanitized.replace(char, '_')
        }
        
        // Remove control characters (0x00-0x1F, 0x7F) but keep Unicode
        sanitized = sanitized.replace(Regex("[\\x00-\\x1f\\x7f]"), "_")
        
        // Ensure the filename is not too long (max 255 characters for most file systems)
        if (sanitized.length > 255) {
            val lastDotIndex = sanitized.lastIndexOf('.')
            if (lastDotIndex > 0 && lastDotIndex < sanitized.length - 1) {
                val name = sanitized.substring(0, lastDotIndex)
                val ext = sanitized.substring(lastDotIndex)
                val maxNameLength = 255 - ext.length
                sanitized = name.take(maxNameLength) + ext
            } else {
                sanitized = sanitized.take(255)
            }
        }
        
        // Ensure the filename doesn't start or end with spaces or dots
        sanitized = sanitized.trim('.', ' ')
        
        // If the filename is empty after sanitization, use a default name
        if (sanitized.isBlank()) {
            sanitized = "unnamed_file"
        }
        
        // Ensure the filename doesn't start with a dash (can cause issues in some systems)
        if (sanitized.startsWith("-")) {
            sanitized = "file$sanitized"
        }
        
        // Ensure the filename doesn't start with reserved names on Windows
        val reservedNames = listOf("CON", "PRN", "AUX", "NUL", "COM1", "COM2", "COM3", "COM4", "COM5", "COM6", "COM7", "COM8", "COM9", "LPT1", "LPT2", "LPT3", "LPT4", "LPT5", "LPT6", "LPT7", "LPT8", "LPT9")
        val upperName = sanitized.uppercase()
        if (reservedNames.any { upperName.startsWith(it) && (upperName.length == it.length || upperName[it.length] == '.') }) {
            sanitized = "file_$sanitized"
        }
        
        println("Sanitized filename: '$fileName' -> '$sanitized'")
        return sanitized
    }

    private fun escapeJsonString(str: String): String {
        return str.replace("\\", "\\\\")
                  .replace("\"", "\\\"")
                  .replace("\b", "\\b")
                  .replace("\n", "\\n")
                  .replace("\r", "\\r")
                  .replace("\t", "\\t")
                  .replace("/", "\\/")
    }

    private fun extractMultipartHeaders(headerData: ByteArray, headerEndIndex: Int): Map<String, String> {
        val headerString = String(headerData, 0, headerEndIndex, Charsets.UTF_8)
        val result = mutableMapOf<String, String>()

        // Extract field name from Content-Disposition header
        val namePattern = Regex("""name\s*=\s*"([^"]+)"""", RegexOption.IGNORE_CASE)
        val nameMatch = namePattern.find(headerString)
        if (nameMatch != null) {
            result["name"] = nameMatch.groupValues[1]
        }

        // Extract filename from Content-Disposition header
        val filenamePatterns = listOf(
            Regex("""filename\*\s*=\s*UTF-8''([^;\r\n]+)""", RegexOption.IGNORE_CASE),
            Regex("""filename\s*=\s*"([^"]+)"""", RegexOption.IGNORE_CASE),
            Regex("""filename\s*=\s*([^;\s\r\n]+)""", RegexOption.IGNORE_CASE)
        )

        for (pattern in filenamePatterns) {
            val match = pattern.find(headerString)
            if (match != null) {
                var filename = match.groupValues[1].trim()
                if (pattern == filenamePatterns[0]) {
                    try {
                        filename = URLDecoder.decode(filename, "UTF-8")
                    } catch (e: Exception) {
                        println("Warning: Could not URL decode filename '$filename': ${e.message}")
                    }
                }
                result["filename"] = filename
                break
            }
        }

        println("Extracted headers: $result")
        return result
    }

    private fun createDirectoryStructure(relativePath: String): Boolean {
        try {
            val file = File(downloadDir, relativePath)
            val parentDir = file.parentFile

            if (parentDir != null && !parentDir.exists()) {
                val created = parentDir.mkdirs()
                println("Created directory structure: ${parentDir.absolutePath} - Success: $created")
                return created
            }
            return true
        } catch (e: Exception) {
            println("Error creating directory structure for '$relativePath': ${e.message}")
            return false
        }
    }

    private fun sanitizeFilePath(filePath: String): String {
        if (filePath.isBlank()) {
            return "unnamed_file"
        }

        // Normalize path separators and remove dangerous patterns
        var sanitized = filePath.replace("\\", "/")

        // Remove any attempts to go up directories
        sanitized = sanitized.replace("../", "").replace("..\\", "")

        // Remove leading slashes
        sanitized = sanitized.removePrefix("/")

        // Split into path components and sanitize each
        val pathComponents = sanitized.split("/")
        val sanitizedComponents = pathComponents.map { component ->
            sanitizeFileName(component)
        }

        return sanitizedComponents.joinToString("/")
    }

    private fun createUniqueFileWithPath(filePath: String): File {
        val file = File(downloadDir, filePath)

        // If file doesn't exist, return it as-is
        if (!file.exists()) {
            return file
        }

        // If file exists, create a unique name
        val parentDir = file.parentFile ?: downloadDir
        val fileName = file.name

        var counter = 1
        var uniqueFile: File

        do {
            val nameWithoutExt = fileName.substringBeforeLast(".", fileName)
            val extension = if (fileName.contains(".")) ".${fileName.substringAfterLast(".")}" else ""
            val uniqueName = "${nameWithoutExt}_$counter$extension"
            uniqueFile = File(parentDir, uniqueName)
            counter++
        } while (uniqueFile.exists())

        return uniqueFile
    }

    // Helper method to debug file locations
    fun getDownloadDirectoryInfo(): String {
        return buildString {
            appendLine("=== DOWNLOAD DIRECTORY INFO ===")
            appendLine("Path: ${downloadDir.absolutePath}")
            appendLine("Exists: ${downloadDir.exists()}")
            appendLine("Writable: ${downloadDir.canWrite()}")
            appendLine("Files:")
            downloadDir.listFiles()?.forEach { file ->
                appendLine("  - ${file.name} (${file.length()} bytes, readable: ${file.canRead()})")
            } ?: appendLine("  No files found or directory not accessible")
            appendLine("===============================")
        }
    }
}
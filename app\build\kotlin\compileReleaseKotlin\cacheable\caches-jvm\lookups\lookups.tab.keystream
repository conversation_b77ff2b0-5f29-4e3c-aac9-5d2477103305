  Manifest android  ACCESS_NETWORK_STATE android.Manifest.permission  ACCESS_WIFI_STATE android.Manifest.permission  INTERNET android.Manifest.permission  READ_EXTERNAL_STORAGE android.Manifest.permission  READ_MEDIA_AUDIO android.Manifest.permission  READ_MEDIA_IMAGES android.Manifest.permission  READ_MEDIA_VIDEO android.Manifest.permission  WRITE_EXTERNAL_STORAGE android.Manifest.permission  SuppressLint android.annotation  Activity android.app  ActivityCompat android.app.Activity  
ContextCompat android.app.Activity  	Exception android.app.Activity  Manifest android.app.Activity  PackageManager android.app.Activity  RobustHttpServer android.app.Activity  String android.app.Activity  Thread android.app.Activity  Toast android.app.Activity  WiFiTransferApp android.app.Activity  android android.app.Activity  
isNotEmpty android.app.Activity  
mutableListOf android.app.Activity  onCreate android.app.Activity  	onDestroy android.app.Activity  requestPermissions android.app.Activity  
setContent android.app.Activity  toTypedArray android.app.Activity  Context android.content  ActivityCompat android.content.Context  
ContextCompat android.content.Context  	Exception android.content.Context  Manifest android.content.Context  PackageManager android.content.Context  RobustHttpServer android.content.Context  String android.content.Context  Thread android.content.Context  Toast android.content.Context  WiFiTransferApp android.content.Context  android android.content.Context  
isNotEmpty android.content.Context  
mutableListOf android.content.Context  
setContent android.content.Context  toTypedArray android.content.Context  ActivityCompat android.content.ContextWrapper  
ContextCompat android.content.ContextWrapper  	Exception android.content.ContextWrapper  Manifest android.content.ContextWrapper  PackageManager android.content.ContextWrapper  RobustHttpServer android.content.ContextWrapper  String android.content.ContextWrapper  Thread android.content.ContextWrapper  Toast android.content.ContextWrapper  WiFiTransferApp android.content.ContextWrapper  android android.content.ContextWrapper  
isNotEmpty android.content.ContextWrapper  
mutableListOf android.content.ContextWrapper  
setContent android.content.ContextWrapper  toTypedArray android.content.ContextWrapper  PackageManager android.content.pm  PERMISSION_GRANTED !android.content.pm.PackageManager  Build 
android.os  Bundle 
android.os  Environment 
android.os  SDK_INT android.os.Build.VERSION  S android.os.Build.VERSION_CODES  TIRAMISU android.os.Build.VERSION_CODES  DIRECTORY_DOWNLOADS android.os.Environment  !getExternalStoragePublicDirectory android.os.Environment  ActivityCompat  android.view.ContextThemeWrapper  
ContextCompat  android.view.ContextThemeWrapper  	Exception  android.view.ContextThemeWrapper  Manifest  android.view.ContextThemeWrapper  PackageManager  android.view.ContextThemeWrapper  RobustHttpServer  android.view.ContextThemeWrapper  String  android.view.ContextThemeWrapper  Thread  android.view.ContextThemeWrapper  Toast  android.view.ContextThemeWrapper  WiFiTransferApp  android.view.ContextThemeWrapper  android  android.view.ContextThemeWrapper  
isNotEmpty  android.view.ContextThemeWrapper  
mutableListOf  android.view.ContextThemeWrapper  
setContent  android.view.ContextThemeWrapper  toTypedArray  android.view.ContextThemeWrapper  Toast android.widget  LENGTH_LONG android.widget.Toast  LENGTH_SHORT android.widget.Toast  makeText android.widget.Toast  show android.widget.Toast  ComponentActivity androidx.activity  ActivityCompat #androidx.activity.ComponentActivity  
ContextCompat #androidx.activity.ComponentActivity  	Exception #androidx.activity.ComponentActivity  Manifest #androidx.activity.ComponentActivity  PackageManager #androidx.activity.ComponentActivity  RobustHttpServer #androidx.activity.ComponentActivity  String #androidx.activity.ComponentActivity  Thread #androidx.activity.ComponentActivity  Toast #androidx.activity.ComponentActivity  WiFiTransferApp #androidx.activity.ComponentActivity  android #androidx.activity.ComponentActivity  
isNotEmpty #androidx.activity.ComponentActivity  
mutableListOf #androidx.activity.ComponentActivity  onCreate #androidx.activity.ComponentActivity  	onDestroy #androidx.activity.ComponentActivity  
setContent #androidx.activity.ComponentActivity  toTypedArray #androidx.activity.ComponentActivity  
setContent androidx.activity.compose  isSystemInDarkTheme androidx.compose.foundation  ActivityCompat "androidx.compose.foundation.layout  	Alignment "androidx.compose.foundation.layout  Arrangement "androidx.compose.foundation.layout  Boolean "androidx.compose.foundation.layout  Bundle "androidx.compose.foundation.layout  Button "androidx.compose.foundation.layout  	ByteArray "androidx.compose.foundation.layout  ByteArrayOutputStream "androidx.compose.foundation.layout  Card "androidx.compose.foundation.layout  CardDefaults "androidx.compose.foundation.layout  Charsets "androidx.compose.foundation.layout  Column "androidx.compose.foundation.layout  ColumnScope "androidx.compose.foundation.layout  ComponentActivity "androidx.compose.foundation.layout  
Composable "androidx.compose.foundation.layout  
ContextCompat "androidx.compose.foundation.layout  Date "androidx.compose.foundation.layout  Environment "androidx.compose.foundation.layout  	Exception "androidx.compose.foundation.layout  File "androidx.compose.foundation.layout  FileInfo "androidx.compose.foundation.layout  FileInputStream "androidx.compose.foundation.layout  FileOutputStream "androidx.compose.foundation.layout  FileUploadContext "androidx.compose.foundation.layout  
FontWeight "androidx.compose.foundation.layout  HttpRequest "androidx.compose.foundation.layout  IOException "androidx.compose.foundation.layout  Inet4Address "androidx.compose.foundation.layout  InetSocketAddress "androidx.compose.foundation.layout  InputStream "androidx.compose.foundation.layout  Int "androidx.compose.foundation.layout  LaunchedEffect "androidx.compose.foundation.layout  
LazyColumn "androidx.compose.foundation.layout  Locale "androidx.compose.foundation.layout  Long "androidx.compose.foundation.layout  Manifest "androidx.compose.foundation.layout  Map "androidx.compose.foundation.layout  
MaterialTheme "androidx.compose.foundation.layout  Modifier "androidx.compose.foundation.layout  MultipartState "androidx.compose.foundation.layout  NetworkInterface "androidx.compose.foundation.layout  OutputStream "androidx.compose.foundation.layout  PackageManager "androidx.compose.foundation.layout  Regex "androidx.compose.foundation.layout  RegexOption "androidx.compose.foundation.layout  RobustHttpServer "androidx.compose.foundation.layout  Row "androidx.compose.foundation.layout  RowScope "androidx.compose.foundation.layout  ServerSocket "androidx.compose.foundation.layout  SimpleDateFormat "androidx.compose.foundation.layout  Socket "androidx.compose.foundation.layout  SocketTimeoutException "androidx.compose.foundation.layout  String "androidx.compose.foundation.layout  
StringBuilder "androidx.compose.foundation.layout  SuppressLint "androidx.compose.foundation.layout  System "androidx.compose.foundation.layout  Text "androidx.compose.foundation.layout  Thread "androidx.compose.foundation.layout  Toast "androidx.compose.foundation.layout  Triple "androidx.compose.foundation.layout  
URLDecoder "androidx.compose.foundation.layout  
URLEncoder "androidx.compose.foundation.layout  Unit "androidx.compose.foundation.layout  WiFiTransferApp "androidx.compose.foundation.layout  also "androidx.compose.foundation.layout  android "androidx.compose.foundation.layout  any "androidx.compose.foundation.layout  apply "androidx.compose.foundation.layout  bufferedReader "androidx.compose.foundation.layout  byteArrayOf "androidx.compose.foundation.layout  
cardColors "androidx.compose.foundation.layout  charArrayOf "androidx.compose.foundation.layout  code "androidx.compose.foundation.layout  
component1 "androidx.compose.foundation.layout  
component2 "androidx.compose.foundation.layout  contains "androidx.compose.foundation.layout  copyOf "androidx.compose.foundation.layout  	emptyList "androidx.compose.foundation.layout  fillMaxSize "androidx.compose.foundation.layout  fillMaxWidth "androidx.compose.foundation.layout  forEach "androidx.compose.foundation.layout  format "androidx.compose.foundation.layout  formatFileSize "androidx.compose.foundation.layout  getValue "androidx.compose.foundation.layout  getWiFiIPAddress "androidx.compose.foundation.layout  indexOf "androidx.compose.foundation.layout  indices "androidx.compose.foundation.layout  invoke "androidx.compose.foundation.layout  isBlank "androidx.compose.foundation.layout  isEmpty "androidx.compose.foundation.layout  
isNotEmpty "androidx.compose.foundation.layout  iterator "androidx.compose.foundation.layout  	javaClass "androidx.compose.foundation.layout  joinToString "androidx.compose.foundation.layout  lastIndexOf "androidx.compose.foundation.layout  let "androidx.compose.foundation.layout  listOf "androidx.compose.foundation.layout  	lowercase "androidx.compose.foundation.layout  map "androidx.compose.foundation.layout  mapOf "androidx.compose.foundation.layout  maxOf "androidx.compose.foundation.layout  minOf "androidx.compose.foundation.layout  
mutableListOf "androidx.compose.foundation.layout  mutableMapOf "androidx.compose.foundation.layout  mutableSetOf "androidx.compose.foundation.layout  mutableStateOf "androidx.compose.foundation.layout  padding "androidx.compose.foundation.layout  
plusAssign "androidx.compose.foundation.layout  port "androidx.compose.foundation.layout  println "androidx.compose.foundation.layout  provideDelegate "androidx.compose.foundation.layout  remember "androidx.compose.foundation.layout  	removeAll "androidx.compose.foundation.layout  removePrefix "androidx.compose.foundation.layout  replace "androidx.compose.foundation.layout  	runServer "androidx.compose.foundation.layout  set "androidx.compose.foundation.layout  setValue "androidx.compose.foundation.layout  split "androidx.compose.foundation.layout  
startsWith "androidx.compose.foundation.layout  	substring "androidx.compose.foundation.layout  substringAfter "androidx.compose.foundation.layout  substringAfterLast "androidx.compose.foundation.layout  substringBeforeLast "androidx.compose.foundation.layout  synchronized "androidx.compose.foundation.layout  take "androidx.compose.foundation.layout  to "androidx.compose.foundation.layout  toByteArray "androidx.compose.foundation.layout  toIntOrNull "androidx.compose.foundation.layout  toTypedArray "androidx.compose.foundation.layout  trim "androidx.compose.foundation.layout  
trimIndent "androidx.compose.foundation.layout  until "androidx.compose.foundation.layout  	uppercase "androidx.compose.foundation.layout  use "androidx.compose.foundation.layout  	writeText "androidx.compose.foundation.layout  HorizontalOrVertical .androidx.compose.foundation.layout.Arrangement  SpaceEvenly .androidx.compose.foundation.layout.Arrangement  	Alignment .androidx.compose.foundation.layout.ColumnScope  Arrangement .androidx.compose.foundation.layout.ColumnScope  Button .androidx.compose.foundation.layout.ColumnScope  Card .androidx.compose.foundation.layout.ColumnScope  CardDefaults .androidx.compose.foundation.layout.ColumnScope  Column .androidx.compose.foundation.layout.ColumnScope  
FontWeight .androidx.compose.foundation.layout.ColumnScope  
LazyColumn .androidx.compose.foundation.layout.ColumnScope  
MaterialTheme .androidx.compose.foundation.layout.ColumnScope  Modifier .androidx.compose.foundation.layout.ColumnScope  Row .androidx.compose.foundation.layout.ColumnScope  Text .androidx.compose.foundation.layout.ColumnScope  
cardColors .androidx.compose.foundation.layout.ColumnScope  dp .androidx.compose.foundation.layout.ColumnScope  fillMaxWidth .androidx.compose.foundation.layout.ColumnScope  formatFileSize .androidx.compose.foundation.layout.ColumnScope  
isNotEmpty .androidx.compose.foundation.layout.ColumnScope  items .androidx.compose.foundation.layout.ColumnScope  padding .androidx.compose.foundation.layout.ColumnScope  sp .androidx.compose.foundation.layout.ColumnScope  Button +androidx.compose.foundation.layout.RowScope  
FontWeight +androidx.compose.foundation.layout.RowScope  
MaterialTheme +androidx.compose.foundation.layout.RowScope  Text +androidx.compose.foundation.layout.RowScope  
LazyColumn  androidx.compose.foundation.lazy  
LazyItemScope  androidx.compose.foundation.lazy  
LazyListScope  androidx.compose.foundation.lazy  items  androidx.compose.foundation.lazy  Card .androidx.compose.foundation.lazy.LazyItemScope  Column .androidx.compose.foundation.lazy.LazyItemScope  
FontWeight .androidx.compose.foundation.lazy.LazyItemScope  
MaterialTheme .androidx.compose.foundation.lazy.LazyItemScope  Modifier .androidx.compose.foundation.lazy.LazyItemScope  Text .androidx.compose.foundation.lazy.LazyItemScope  dp .androidx.compose.foundation.lazy.LazyItemScope  fillMaxWidth .androidx.compose.foundation.lazy.LazyItemScope  formatFileSize .androidx.compose.foundation.lazy.LazyItemScope  padding .androidx.compose.foundation.lazy.LazyItemScope  sp .androidx.compose.foundation.lazy.LazyItemScope  Card .androidx.compose.foundation.lazy.LazyListScope  Column .androidx.compose.foundation.lazy.LazyListScope  
FontWeight .androidx.compose.foundation.lazy.LazyListScope  
MaterialTheme .androidx.compose.foundation.lazy.LazyListScope  Modifier .androidx.compose.foundation.lazy.LazyListScope  Text .androidx.compose.foundation.lazy.LazyListScope  dp .androidx.compose.foundation.lazy.LazyListScope  fillMaxWidth .androidx.compose.foundation.lazy.LazyListScope  formatFileSize .androidx.compose.foundation.lazy.LazyListScope  items .androidx.compose.foundation.lazy.LazyListScope  padding .androidx.compose.foundation.lazy.LazyListScope  sp .androidx.compose.foundation.lazy.LazyListScope  ActivityCompat androidx.compose.material3  	Alignment androidx.compose.material3  Arrangement androidx.compose.material3  Boolean androidx.compose.material3  Bundle androidx.compose.material3  Button androidx.compose.material3  	ByteArray androidx.compose.material3  ByteArrayOutputStream androidx.compose.material3  Card androidx.compose.material3  
CardColors androidx.compose.material3  CardDefaults androidx.compose.material3  Charsets androidx.compose.material3  ColorScheme androidx.compose.material3  Column androidx.compose.material3  ComponentActivity androidx.compose.material3  
Composable androidx.compose.material3  
ContextCompat androidx.compose.material3  Date androidx.compose.material3  Environment androidx.compose.material3  	Exception androidx.compose.material3  File androidx.compose.material3  FileInfo androidx.compose.material3  FileInputStream androidx.compose.material3  FileOutputStream androidx.compose.material3  FileUploadContext androidx.compose.material3  
FontWeight androidx.compose.material3  HttpRequest androidx.compose.material3  IOException androidx.compose.material3  Inet4Address androidx.compose.material3  InetSocketAddress androidx.compose.material3  InputStream androidx.compose.material3  Int androidx.compose.material3  LaunchedEffect androidx.compose.material3  
LazyColumn androidx.compose.material3  Locale androidx.compose.material3  Long androidx.compose.material3  Manifest androidx.compose.material3  Map androidx.compose.material3  
MaterialTheme androidx.compose.material3  Modifier androidx.compose.material3  MultipartState androidx.compose.material3  NetworkInterface androidx.compose.material3  OutputStream androidx.compose.material3  PackageManager androidx.compose.material3  Regex androidx.compose.material3  RegexOption androidx.compose.material3  RobustHttpServer androidx.compose.material3  Row androidx.compose.material3  ServerSocket androidx.compose.material3  SimpleDateFormat androidx.compose.material3  Socket androidx.compose.material3  SocketTimeoutException androidx.compose.material3  String androidx.compose.material3  
StringBuilder androidx.compose.material3  SuppressLint androidx.compose.material3  System androidx.compose.material3  Text androidx.compose.material3  Thread androidx.compose.material3  Toast androidx.compose.material3  Triple androidx.compose.material3  
Typography androidx.compose.material3  
URLDecoder androidx.compose.material3  
URLEncoder androidx.compose.material3  Unit androidx.compose.material3  WiFiTransferApp androidx.compose.material3  also androidx.compose.material3  android androidx.compose.material3  any androidx.compose.material3  apply androidx.compose.material3  bufferedReader androidx.compose.material3  byteArrayOf androidx.compose.material3  
cardColors androidx.compose.material3  charArrayOf androidx.compose.material3  code androidx.compose.material3  
component1 androidx.compose.material3  
component2 androidx.compose.material3  contains androidx.compose.material3  copyOf androidx.compose.material3  darkColorScheme androidx.compose.material3  dynamicDarkColorScheme androidx.compose.material3  dynamicLightColorScheme androidx.compose.material3  	emptyList androidx.compose.material3  fillMaxSize androidx.compose.material3  fillMaxWidth androidx.compose.material3  forEach androidx.compose.material3  format androidx.compose.material3  formatFileSize androidx.compose.material3  getValue androidx.compose.material3  getWiFiIPAddress androidx.compose.material3  indexOf androidx.compose.material3  indices androidx.compose.material3  invoke androidx.compose.material3  isBlank androidx.compose.material3  isEmpty androidx.compose.material3  
isNotEmpty androidx.compose.material3  iterator androidx.compose.material3  	javaClass androidx.compose.material3  joinToString androidx.compose.material3  lastIndexOf androidx.compose.material3  let androidx.compose.material3  lightColorScheme androidx.compose.material3  listOf androidx.compose.material3  	lowercase androidx.compose.material3  map androidx.compose.material3  mapOf androidx.compose.material3  maxOf androidx.compose.material3  minOf androidx.compose.material3  
mutableListOf androidx.compose.material3  mutableMapOf androidx.compose.material3  mutableSetOf androidx.compose.material3  mutableStateOf androidx.compose.material3  padding androidx.compose.material3  
plusAssign androidx.compose.material3  port androidx.compose.material3  println androidx.compose.material3  provideDelegate androidx.compose.material3  remember androidx.compose.material3  	removeAll androidx.compose.material3  removePrefix androidx.compose.material3  replace androidx.compose.material3  	runServer androidx.compose.material3  set androidx.compose.material3  setValue androidx.compose.material3  split androidx.compose.material3  
startsWith androidx.compose.material3  	substring androidx.compose.material3  substringAfter androidx.compose.material3  substringAfterLast androidx.compose.material3  substringBeforeLast androidx.compose.material3  synchronized androidx.compose.material3  take androidx.compose.material3  to androidx.compose.material3  toByteArray androidx.compose.material3  toIntOrNull androidx.compose.material3  toTypedArray androidx.compose.material3  trim androidx.compose.material3  
trimIndent androidx.compose.material3  until androidx.compose.material3  	uppercase androidx.compose.material3  use androidx.compose.material3  	writeText androidx.compose.material3  
cardColors 'androidx.compose.material3.CardDefaults  onPrimaryContainer &androidx.compose.material3.ColorScheme  onSurfaceVariant &androidx.compose.material3.ColorScheme  primary &androidx.compose.material3.ColorScheme  primaryContainer &androidx.compose.material3.ColorScheme  colorScheme (androidx.compose.material3.MaterialTheme  ActivityCompat androidx.compose.runtime  	Alignment androidx.compose.runtime  Arrangement androidx.compose.runtime  Boolean androidx.compose.runtime  Bundle androidx.compose.runtime  Button androidx.compose.runtime  	ByteArray androidx.compose.runtime  ByteArrayOutputStream androidx.compose.runtime  Card androidx.compose.runtime  CardDefaults androidx.compose.runtime  Charsets androidx.compose.runtime  Column androidx.compose.runtime  ComponentActivity androidx.compose.runtime  
Composable androidx.compose.runtime  
ContextCompat androidx.compose.runtime  Date androidx.compose.runtime  Environment androidx.compose.runtime  	Exception androidx.compose.runtime  File androidx.compose.runtime  FileInfo androidx.compose.runtime  FileInputStream androidx.compose.runtime  FileOutputStream androidx.compose.runtime  FileUploadContext androidx.compose.runtime  
FontWeight androidx.compose.runtime  HttpRequest androidx.compose.runtime  IOException androidx.compose.runtime  Inet4Address androidx.compose.runtime  InetSocketAddress androidx.compose.runtime  InputStream androidx.compose.runtime  Int androidx.compose.runtime  LaunchedEffect androidx.compose.runtime  
LazyColumn androidx.compose.runtime  Locale androidx.compose.runtime  Long androidx.compose.runtime  Manifest androidx.compose.runtime  Map androidx.compose.runtime  
MaterialTheme androidx.compose.runtime  Modifier androidx.compose.runtime  MultipartState androidx.compose.runtime  MutableState androidx.compose.runtime  NetworkInterface androidx.compose.runtime  OutputStream androidx.compose.runtime  PackageManager androidx.compose.runtime  ProvidableCompositionLocal androidx.compose.runtime  Regex androidx.compose.runtime  RegexOption androidx.compose.runtime  RobustHttpServer androidx.compose.runtime  Row androidx.compose.runtime  ServerSocket androidx.compose.runtime  SimpleDateFormat androidx.compose.runtime  Socket androidx.compose.runtime  SocketTimeoutException androidx.compose.runtime  String androidx.compose.runtime  
StringBuilder androidx.compose.runtime  SuppressLint androidx.compose.runtime  System androidx.compose.runtime  Text androidx.compose.runtime  Thread androidx.compose.runtime  Toast androidx.compose.runtime  Triple androidx.compose.runtime  
URLDecoder androidx.compose.runtime  
URLEncoder androidx.compose.runtime  Unit androidx.compose.runtime  WiFiTransferApp androidx.compose.runtime  also androidx.compose.runtime  android androidx.compose.runtime  any androidx.compose.runtime  apply androidx.compose.runtime  bufferedReader androidx.compose.runtime  byteArrayOf androidx.compose.runtime  
cardColors androidx.compose.runtime  charArrayOf androidx.compose.runtime  code androidx.compose.runtime  
component1 androidx.compose.runtime  
component2 androidx.compose.runtime  contains androidx.compose.runtime  copyOf androidx.compose.runtime  	emptyList androidx.compose.runtime  fillMaxSize androidx.compose.runtime  fillMaxWidth androidx.compose.runtime  forEach androidx.compose.runtime  format androidx.compose.runtime  formatFileSize androidx.compose.runtime  getValue androidx.compose.runtime  getWiFiIPAddress androidx.compose.runtime  indexOf androidx.compose.runtime  indices androidx.compose.runtime  invoke androidx.compose.runtime  isBlank androidx.compose.runtime  isEmpty androidx.compose.runtime  
isNotEmpty androidx.compose.runtime  iterator androidx.compose.runtime  	javaClass androidx.compose.runtime  joinToString androidx.compose.runtime  lastIndexOf androidx.compose.runtime  let androidx.compose.runtime  listOf androidx.compose.runtime  	lowercase androidx.compose.runtime  map androidx.compose.runtime  mapOf androidx.compose.runtime  maxOf androidx.compose.runtime  minOf androidx.compose.runtime  
mutableListOf androidx.compose.runtime  mutableMapOf androidx.compose.runtime  mutableSetOf androidx.compose.runtime  mutableStateOf androidx.compose.runtime  padding androidx.compose.runtime  
plusAssign androidx.compose.runtime  port androidx.compose.runtime  println androidx.compose.runtime  provideDelegate androidx.compose.runtime  remember androidx.compose.runtime  	removeAll androidx.compose.runtime  removePrefix androidx.compose.runtime  replace androidx.compose.runtime  	runServer androidx.compose.runtime  set androidx.compose.runtime  setValue androidx.compose.runtime  split androidx.compose.runtime  
startsWith androidx.compose.runtime  	substring androidx.compose.runtime  substringAfter androidx.compose.runtime  substringAfterLast androidx.compose.runtime  substringBeforeLast androidx.compose.runtime  synchronized androidx.compose.runtime  take androidx.compose.runtime  to androidx.compose.runtime  toByteArray androidx.compose.runtime  toIntOrNull androidx.compose.runtime  toTypedArray androidx.compose.runtime  trim androidx.compose.runtime  
trimIndent androidx.compose.runtime  until androidx.compose.runtime  	uppercase androidx.compose.runtime  use androidx.compose.runtime  	writeText androidx.compose.runtime  
getCurrent )androidx.compose.runtime.CompositionLocal  setValue %androidx.compose.runtime.MutableState  current 3androidx.compose.runtime.ProvidableCompositionLocal  ComposableFunction0 !androidx.compose.runtime.internal  ComposableFunction1 !androidx.compose.runtime.internal  ComposableFunction2 !androidx.compose.runtime.internal  	Alignment androidx.compose.ui  Modifier androidx.compose.ui  CenterHorizontally androidx.compose.ui.Alignment  CenterVertically androidx.compose.ui.Alignment  	Companion androidx.compose.ui.Alignment  
Horizontal androidx.compose.ui.Alignment  Vertical androidx.compose.ui.Alignment  CenterHorizontally 'androidx.compose.ui.Alignment.Companion  CenterVertically 'androidx.compose.ui.Alignment.Companion  	Companion androidx.compose.ui.Modifier  fillMaxSize androidx.compose.ui.Modifier  fillMaxWidth androidx.compose.ui.Modifier  padding androidx.compose.ui.Modifier  fillMaxSize &androidx.compose.ui.Modifier.Companion  fillMaxWidth &androidx.compose.ui.Modifier.Companion  padding &androidx.compose.ui.Modifier.Companion  Color androidx.compose.ui.graphics  LocalContext androidx.compose.ui.platform  	TextStyle androidx.compose.ui.text  
FontFamily androidx.compose.ui.text.font  
FontWeight androidx.compose.ui.text.font  SystemFontFamily androidx.compose.ui.text.font  	Companion (androidx.compose.ui.text.font.FontFamily  Default (androidx.compose.ui.text.font.FontFamily  Default 2androidx.compose.ui.text.font.FontFamily.Companion  Bold (androidx.compose.ui.text.font.FontWeight  	Companion (androidx.compose.ui.text.font.FontWeight  Medium (androidx.compose.ui.text.font.FontWeight  Normal (androidx.compose.ui.text.font.FontWeight  Bold 2androidx.compose.ui.text.font.FontWeight.Companion  Medium 2androidx.compose.ui.text.font.FontWeight.Companion  Normal 2androidx.compose.ui.text.font.FontWeight.Companion  Dp androidx.compose.ui.unit  TextUnit androidx.compose.ui.unit  dp androidx.compose.ui.unit  getDp androidx.compose.ui.unit  sp androidx.compose.ui.unit  ActivityCompat androidx.core.app  requestPermissions  androidx.core.app.ActivityCompat  ActivityCompat #androidx.core.app.ComponentActivity  Boolean #androidx.core.app.ComponentActivity  Bundle #androidx.core.app.ComponentActivity  
ContextCompat #androidx.core.app.ComponentActivity  	Exception #androidx.core.app.ComponentActivity  Manifest #androidx.core.app.ComponentActivity  PackageManager #androidx.core.app.ComponentActivity  RobustHttpServer #androidx.core.app.ComponentActivity  String #androidx.core.app.ComponentActivity  Thread #androidx.core.app.ComponentActivity  Toast #androidx.core.app.ComponentActivity  WiFiTransferApp #androidx.core.app.ComponentActivity  android #androidx.core.app.ComponentActivity  
isNotEmpty #androidx.core.app.ComponentActivity  
mutableListOf #androidx.core.app.ComponentActivity  
setContent #androidx.core.app.ComponentActivity  toTypedArray #androidx.core.app.ComponentActivity  
ContextCompat androidx.core.content  checkSelfPermission #androidx.core.content.ContextCompat  ActivityCompat com.xeno.wififiletransfer  	Alignment com.xeno.wififiletransfer  Arrangement com.xeno.wififiletransfer  Boolean com.xeno.wififiletransfer  Bundle com.xeno.wififiletransfer  Button com.xeno.wififiletransfer  	ByteArray com.xeno.wififiletransfer  ByteArrayOutputStream com.xeno.wififiletransfer  Card com.xeno.wififiletransfer  CardDefaults com.xeno.wififiletransfer  Charsets com.xeno.wififiletransfer  Column com.xeno.wififiletransfer  ComponentActivity com.xeno.wififiletransfer  
Composable com.xeno.wififiletransfer  
ContextCompat com.xeno.wififiletransfer  Date com.xeno.wififiletransfer  Environment com.xeno.wififiletransfer  	Exception com.xeno.wififiletransfer  File com.xeno.wififiletransfer  FileInfo com.xeno.wififiletransfer  FileInputStream com.xeno.wififiletransfer  FileOutputStream com.xeno.wififiletransfer  FileUploadContext com.xeno.wififiletransfer  
FontWeight com.xeno.wififiletransfer  HttpRequest com.xeno.wififiletransfer  IOException com.xeno.wififiletransfer  Inet4Address com.xeno.wififiletransfer  InetSocketAddress com.xeno.wififiletransfer  InputStream com.xeno.wififiletransfer  Int com.xeno.wififiletransfer  LaunchedEffect com.xeno.wififiletransfer  
LazyColumn com.xeno.wififiletransfer  Locale com.xeno.wififiletransfer  Long com.xeno.wififiletransfer  MainActivity com.xeno.wififiletransfer  Manifest com.xeno.wififiletransfer  Map com.xeno.wififiletransfer  
MaterialTheme com.xeno.wififiletransfer  Modifier com.xeno.wififiletransfer  MultipartState com.xeno.wififiletransfer  NetworkInterface com.xeno.wififiletransfer  OutputStream com.xeno.wififiletransfer  PackageManager com.xeno.wififiletransfer  Regex com.xeno.wififiletransfer  RegexOption com.xeno.wififiletransfer  RobustHttpServer com.xeno.wififiletransfer  Row com.xeno.wififiletransfer  ServerSocket com.xeno.wififiletransfer  SimpleDateFormat com.xeno.wififiletransfer  Socket com.xeno.wififiletransfer  SocketTimeoutException com.xeno.wififiletransfer  String com.xeno.wififiletransfer  
StringBuilder com.xeno.wififiletransfer  SuppressLint com.xeno.wififiletransfer  System com.xeno.wififiletransfer  Text com.xeno.wififiletransfer  Thread com.xeno.wififiletransfer  Toast com.xeno.wififiletransfer  Triple com.xeno.wififiletransfer  
URLDecoder com.xeno.wififiletransfer  
URLEncoder com.xeno.wififiletransfer  Unit com.xeno.wififiletransfer  WiFiTransferApp com.xeno.wififiletransfer  also com.xeno.wififiletransfer  android com.xeno.wififiletransfer  any com.xeno.wififiletransfer  apply com.xeno.wififiletransfer  bufferedReader com.xeno.wififiletransfer  byteArrayOf com.xeno.wififiletransfer  
cardColors com.xeno.wififiletransfer  charArrayOf com.xeno.wififiletransfer  code com.xeno.wififiletransfer  
component1 com.xeno.wififiletransfer  
component2 com.xeno.wififiletransfer  contains com.xeno.wififiletransfer  copyOf com.xeno.wififiletransfer  	emptyList com.xeno.wififiletransfer  fillMaxSize com.xeno.wififiletransfer  fillMaxWidth com.xeno.wififiletransfer  forEach com.xeno.wififiletransfer  format com.xeno.wififiletransfer  formatFileSize com.xeno.wififiletransfer  getValue com.xeno.wififiletransfer  getWiFiIPAddress com.xeno.wififiletransfer  indexOf com.xeno.wififiletransfer  indices com.xeno.wififiletransfer  invoke com.xeno.wififiletransfer  isBlank com.xeno.wififiletransfer  isEmpty com.xeno.wififiletransfer  
isNotEmpty com.xeno.wififiletransfer  iterator com.xeno.wififiletransfer  	javaClass com.xeno.wififiletransfer  joinToString com.xeno.wififiletransfer  lastIndexOf com.xeno.wififiletransfer  let com.xeno.wififiletransfer  listOf com.xeno.wififiletransfer  	lowercase com.xeno.wififiletransfer  map com.xeno.wififiletransfer  mapOf com.xeno.wififiletransfer  maxOf com.xeno.wififiletransfer  minOf com.xeno.wififiletransfer  
mutableListOf com.xeno.wififiletransfer  mutableMapOf com.xeno.wififiletransfer  mutableSetOf com.xeno.wififiletransfer  mutableStateOf com.xeno.wififiletransfer  padding com.xeno.wififiletransfer  
plusAssign com.xeno.wififiletransfer  port com.xeno.wififiletransfer  println com.xeno.wififiletransfer  provideDelegate com.xeno.wififiletransfer  remember com.xeno.wififiletransfer  	removeAll com.xeno.wififiletransfer  removePrefix com.xeno.wififiletransfer  replace com.xeno.wififiletransfer  	runServer com.xeno.wififiletransfer  set com.xeno.wififiletransfer  setValue com.xeno.wififiletransfer  split com.xeno.wififiletransfer  
startsWith com.xeno.wififiletransfer  	substring com.xeno.wififiletransfer  substringAfter com.xeno.wififiletransfer  substringAfterLast com.xeno.wififiletransfer  substringBeforeLast com.xeno.wififiletransfer  synchronized com.xeno.wififiletransfer  take com.xeno.wififiletransfer  to com.xeno.wififiletransfer  toByteArray com.xeno.wififiletransfer  toIntOrNull com.xeno.wififiletransfer  toTypedArray com.xeno.wififiletransfer  trim com.xeno.wififiletransfer  
trimIndent com.xeno.wififiletransfer  until com.xeno.wififiletransfer  	uppercase com.xeno.wififiletransfer  use com.xeno.wififiletransfer  	writeText com.xeno.wififiletransfer  name "com.xeno.wififiletransfer.FileInfo  size "com.xeno.wififiletransfer.FileInfo  	timestamp "com.xeno.wififiletransfer.FileInfo  ActivityCompat &com.xeno.wififiletransfer.MainActivity  
ContextCompat &com.xeno.wififiletransfer.MainActivity  Manifest &com.xeno.wififiletransfer.MainActivity  PERMISSION_REQUEST_CODE &com.xeno.wififiletransfer.MainActivity  PackageManager &com.xeno.wififiletransfer.MainActivity  RobustHttpServer &com.xeno.wififiletransfer.MainActivity  Thread &com.xeno.wififiletransfer.MainActivity  Toast &com.xeno.wififiletransfer.MainActivity  WiFiTransferApp &com.xeno.wififiletransfer.MainActivity  android &com.xeno.wififiletransfer.MainActivity  hasRequiredPermissions &com.xeno.wififiletransfer.MainActivity  
httpServer &com.xeno.wififiletransfer.MainActivity  
isNotEmpty &com.xeno.wififiletransfer.MainActivity  
mutableListOf &com.xeno.wififiletransfer.MainActivity  requestPermissions &com.xeno.wififiletransfer.MainActivity  
restartServer &com.xeno.wififiletransfer.MainActivity  
setContent &com.xeno.wififiletransfer.MainActivity  startServer &com.xeno.wififiletransfer.MainActivity  
stopServer &com.xeno.wififiletransfer.MainActivity  toTypedArray &com.xeno.wififiletransfer.MainActivity  Boolean *com.xeno.wififiletransfer.RobustHttpServer  	ByteArray *com.xeno.wififiletransfer.RobustHttpServer  ByteArrayOutputStream *com.xeno.wififiletransfer.RobustHttpServer  Charsets *com.xeno.wififiletransfer.RobustHttpServer  Date *com.xeno.wififiletransfer.RobustHttpServer  Environment *com.xeno.wififiletransfer.RobustHttpServer  	Exception *com.xeno.wififiletransfer.RobustHttpServer  File *com.xeno.wififiletransfer.RobustHttpServer  FileInputStream *com.xeno.wififiletransfer.RobustHttpServer  FileOutputStream *com.xeno.wififiletransfer.RobustHttpServer  FileUploadContext *com.xeno.wififiletransfer.RobustHttpServer  HttpRequest *com.xeno.wififiletransfer.RobustHttpServer  IOException *com.xeno.wififiletransfer.RobustHttpServer  InetSocketAddress *com.xeno.wififiletransfer.RobustHttpServer  InputStream *com.xeno.wififiletransfer.RobustHttpServer  Int *com.xeno.wififiletransfer.RobustHttpServer  Locale *com.xeno.wififiletransfer.RobustHttpServer  Long *com.xeno.wififiletransfer.RobustHttpServer  Map *com.xeno.wififiletransfer.RobustHttpServer  MultipartState *com.xeno.wififiletransfer.RobustHttpServer  OutputStream *com.xeno.wififiletransfer.RobustHttpServer  Regex *com.xeno.wififiletransfer.RobustHttpServer  RegexOption *com.xeno.wififiletransfer.RobustHttpServer  ServerSocket *com.xeno.wififiletransfer.RobustHttpServer  SimpleDateFormat *com.xeno.wififiletransfer.RobustHttpServer  Socket *com.xeno.wififiletransfer.RobustHttpServer  SocketTimeoutException *com.xeno.wififiletransfer.RobustHttpServer  String *com.xeno.wififiletransfer.RobustHttpServer  
StringBuilder *com.xeno.wififiletransfer.RobustHttpServer  System *com.xeno.wififiletransfer.RobustHttpServer  Thread *com.xeno.wififiletransfer.RobustHttpServer  Triple *com.xeno.wififiletransfer.RobustHttpServer  
URLDecoder *com.xeno.wififiletransfer.RobustHttpServer  
URLEncoder *com.xeno.wififiletransfer.RobustHttpServer  also *com.xeno.wififiletransfer.RobustHttpServer  any *com.xeno.wififiletransfer.RobustHttpServer  apply *com.xeno.wififiletransfer.RobustHttpServer  bufferedReader *com.xeno.wififiletransfer.RobustHttpServer  byteArrayOf *com.xeno.wififiletransfer.RobustHttpServer  charArrayOf *com.xeno.wififiletransfer.RobustHttpServer  cleanupFinishedThreads *com.xeno.wififiletransfer.RobustHttpServer  
clientThreads *com.xeno.wififiletransfer.RobustHttpServer  code *com.xeno.wififiletransfer.RobustHttpServer  
component1 *com.xeno.wififiletransfer.RobustHttpServer  
component2 *com.xeno.wififiletransfer.RobustHttpServer  contains *com.xeno.wififiletransfer.RobustHttpServer  copyOf *com.xeno.wififiletransfer.RobustHttpServer  createDirectoryStructure *com.xeno.wififiletransfer.RobustHttpServer  createUniqueFile *com.xeno.wififiletransfer.RobustHttpServer  createUniqueFileWithPath *com.xeno.wififiletransfer.RobustHttpServer  downloadDir *com.xeno.wififiletransfer.RobustHttpServer  	emptyList *com.xeno.wififiletransfer.RobustHttpServer  escapeJsonString *com.xeno.wififiletransfer.RobustHttpServer  extractFilenameFromHeaders *com.xeno.wififiletransfer.RobustHttpServer  extractMultipartHeaders *com.xeno.wififiletransfer.RobustHttpServer  findBoundaryAcrossChunks *com.xeno.wififiletransfer.RobustHttpServer  findBoundaryInChunk *com.xeno.wififiletransfer.RobustHttpServer  
findHeaderEnd *com.xeno.wififiletransfer.RobustHttpServer  forEach *com.xeno.wififiletransfer.RobustHttpServer  formatFileSize *com.xeno.wififiletransfer.RobustHttpServer  handleClientSafely *com.xeno.wififiletransfer.RobustHttpServer  handleGetRequest *com.xeno.wififiletransfer.RobustHttpServer  handleHttpRequest *com.xeno.wififiletransfer.RobustHttpServer  handleLegacyUpload *com.xeno.wififiletransfer.RobustHttpServer  handleMultiFileUpload *com.xeno.wififiletransfer.RobustHttpServer  handleOptionsRequest *com.xeno.wififiletransfer.RobustHttpServer  handlePartialBoundary *com.xeno.wififiletransfer.RobustHttpServer  handlePostRequest *com.xeno.wififiletransfer.RobustHttpServer  handleRealFileUpload *com.xeno.wififiletransfer.RobustHttpServer  handleRobustMultipartUpload *com.xeno.wififiletransfer.RobustHttpServer  handleUltraSimpleUpload *com.xeno.wififiletransfer.RobustHttpServer  indexOf *com.xeno.wififiletransfer.RobustHttpServer  indices *com.xeno.wififiletransfer.RobustHttpServer  initializeServer *com.xeno.wififiletransfer.RobustHttpServer  invoke *com.xeno.wififiletransfer.RobustHttpServer  isBlank *com.xeno.wififiletransfer.RobustHttpServer  isEmpty *com.xeno.wififiletransfer.RobustHttpServer  
isNotEmpty *com.xeno.wififiletransfer.RobustHttpServer  isPartialBoundaryAtEnd *com.xeno.wififiletransfer.RobustHttpServer  isPortAvailable *com.xeno.wififiletransfer.RobustHttpServer  	isRunning *com.xeno.wififiletransfer.RobustHttpServer  iterator *com.xeno.wififiletransfer.RobustHttpServer  	javaClass *com.xeno.wififiletransfer.RobustHttpServer  joinToString *com.xeno.wififiletransfer.RobustHttpServer  lastIndexOf *com.xeno.wififiletransfer.RobustHttpServer  let *com.xeno.wififiletransfer.RobustHttpServer  listOf *com.xeno.wififiletransfer.RobustHttpServer  	lowercase *com.xeno.wififiletransfer.RobustHttpServer  map *com.xeno.wififiletransfer.RobustHttpServer  mapOf *com.xeno.wififiletransfer.RobustHttpServer  maxConcurrentConnections *com.xeno.wififiletransfer.RobustHttpServer  maxOf *com.xeno.wififiletransfer.RobustHttpServer  minOf *com.xeno.wififiletransfer.RobustHttpServer  
mutableListOf *com.xeno.wififiletransfer.RobustHttpServer  mutableMapOf *com.xeno.wififiletransfer.RobustHttpServer  mutableSetOf *com.xeno.wififiletransfer.RobustHttpServer  parseHttpRequest *com.xeno.wififiletransfer.RobustHttpServer  
plusAssign *com.xeno.wififiletransfer.RobustHttpServer  port *com.xeno.wififiletransfer.RobustHttpServer  println *com.xeno.wififiletransfer.RobustHttpServer  readTimeout *com.xeno.wififiletransfer.RobustHttpServer  	removeAll *com.xeno.wififiletransfer.RobustHttpServer  removePrefix *com.xeno.wififiletransfer.RobustHttpServer  replace *com.xeno.wififiletransfer.RobustHttpServer  	runServer *com.xeno.wififiletransfer.RobustHttpServer  sanitizeFileName *com.xeno.wififiletransfer.RobustHttpServer  sanitizeFilePath *com.xeno.wififiletransfer.RobustHttpServer  sendErrorResponse *com.xeno.wififiletransfer.RobustHttpServer  sendJsonResponse *com.xeno.wififiletransfer.RobustHttpServer  serve400 *com.xeno.wififiletransfer.RobustHttpServer  serve404 *com.xeno.wififiletransfer.RobustHttpServer  serve500 *com.xeno.wififiletransfer.RobustHttpServer  	serveFile *com.xeno.wififiletransfer.RobustHttpServer  
serveFileList *com.xeno.wififiletransfer.RobustHttpServer  
serveMainPage *com.xeno.wififiletransfer.RobustHttpServer  
serveResponse *com.xeno.wififiletransfer.RobustHttpServer  serveResponseWithCacheBusting *com.xeno.wififiletransfer.RobustHttpServer  
serveTestPage *com.xeno.wififiletransfer.RobustHttpServer  serverSocket *com.xeno.wififiletransfer.RobustHttpServer  set *com.xeno.wififiletransfer.RobustHttpServer  split *com.xeno.wififiletransfer.RobustHttpServer  start *com.xeno.wififiletransfer.RobustHttpServer  
startsWith *com.xeno.wififiletransfer.RobustHttpServer  stop *com.xeno.wififiletransfer.RobustHttpServer  	substring *com.xeno.wififiletransfer.RobustHttpServer  substringAfter *com.xeno.wififiletransfer.RobustHttpServer  substringAfterLast *com.xeno.wififiletransfer.RobustHttpServer  substringBeforeLast *com.xeno.wififiletransfer.RobustHttpServer  synchronized *com.xeno.wififiletransfer.RobustHttpServer  take *com.xeno.wififiletransfer.RobustHttpServer  to *com.xeno.wififiletransfer.RobustHttpServer  toByteArray *com.xeno.wififiletransfer.RobustHttpServer  toIntOrNull *com.xeno.wififiletransfer.RobustHttpServer  trim *com.xeno.wififiletransfer.RobustHttpServer  
trimIndent *com.xeno.wififiletransfer.RobustHttpServer  until *com.xeno.wififiletransfer.RobustHttpServer  	uppercase *com.xeno.wififiletransfer.RobustHttpServer  use *com.xeno.wififiletransfer.RobustHttpServer  	writeText *com.xeno.wififiletransfer.RobustHttpServer  bytesWritten <com.xeno.wififiletransfer.RobustHttpServer.FileUploadContext  file <com.xeno.wififiletransfer.RobustHttpServer.FileUploadContext  outputStream <com.xeno.wififiletransfer.RobustHttpServer.FileUploadContext  
contentLength 6com.xeno.wififiletransfer.RobustHttpServer.HttpRequest  headers 6com.xeno.wififiletransfer.RobustHttpServer.HttpRequest  method 6com.xeno.wififiletransfer.RobustHttpServer.HttpRequest  path 6com.xeno.wififiletransfer.RobustHttpServer.HttpRequest  COMPLETE 9com.xeno.wififiletransfer.RobustHttpServer.MultipartState  READING_FILE_CONTENT 9com.xeno.wififiletransfer.RobustHttpServer.MultipartState  READING_HEADERS 9com.xeno.wififiletransfer.RobustHttpServer.MultipartState  Boolean "com.xeno.wififiletransfer.ui.theme  Build "com.xeno.wififiletransfer.ui.theme  
Composable "com.xeno.wififiletransfer.ui.theme  DarkColorScheme "com.xeno.wififiletransfer.ui.theme  
FontFamily "com.xeno.wififiletransfer.ui.theme  
FontWeight "com.xeno.wififiletransfer.ui.theme  LightColorScheme "com.xeno.wififiletransfer.ui.theme  Pink40 "com.xeno.wififiletransfer.ui.theme  Pink80 "com.xeno.wififiletransfer.ui.theme  Purple40 "com.xeno.wififiletransfer.ui.theme  Purple80 "com.xeno.wififiletransfer.ui.theme  PurpleGrey40 "com.xeno.wififiletransfer.ui.theme  PurpleGrey80 "com.xeno.wififiletransfer.ui.theme  
Typography "com.xeno.wififiletransfer.ui.theme  Unit "com.xeno.wififiletransfer.ui.theme  WiFiFileTransferTheme "com.xeno.wififiletransfer.ui.theme  ActivityCompat java.io  	Alignment java.io  Arrangement java.io  Boolean java.io  BufferedReader java.io  Bundle java.io  Button java.io  	ByteArray java.io  ByteArrayOutputStream java.io  Card java.io  CardDefaults java.io  Charsets java.io  Column java.io  ComponentActivity java.io  
Composable java.io  
ContextCompat java.io  Date java.io  Environment java.io  	Exception java.io  File java.io  FileInfo java.io  FileInputStream java.io  FileOutputStream java.io  FileUploadContext java.io  
FontWeight java.io  HttpRequest java.io  IOException java.io  Inet4Address java.io  InetSocketAddress java.io  InputStream java.io  Int java.io  LaunchedEffect java.io  
LazyColumn java.io  Locale java.io  Long java.io  Manifest java.io  Map java.io  
MaterialTheme java.io  Modifier java.io  MultipartState java.io  NetworkInterface java.io  OutputStream java.io  PackageManager java.io  Regex java.io  RegexOption java.io  RobustHttpServer java.io  Row java.io  ServerSocket java.io  SimpleDateFormat java.io  Socket java.io  SocketTimeoutException java.io  String java.io  
StringBuilder java.io  SuppressLint java.io  System java.io  Text java.io  Thread java.io  Toast java.io  Triple java.io  
URLDecoder java.io  
URLEncoder java.io  Unit java.io  WiFiTransferApp java.io  also java.io  android java.io  any java.io  apply java.io  bufferedReader java.io  byteArrayOf java.io  
cardColors java.io  charArrayOf java.io  code java.io  
component1 java.io  
component2 java.io  contains java.io  copyOf java.io  	emptyList java.io  fillMaxSize java.io  fillMaxWidth java.io  forEach java.io  format java.io  formatFileSize java.io  getValue java.io  getWiFiIPAddress java.io  indexOf java.io  indices java.io  invoke java.io  isBlank java.io  isEmpty java.io  
isNotEmpty java.io  iterator java.io  	javaClass java.io  joinToString java.io  lastIndexOf java.io  let java.io  listOf java.io  	lowercase java.io  map java.io  mapOf java.io  maxOf java.io  minOf java.io  
mutableListOf java.io  mutableMapOf java.io  mutableSetOf java.io  mutableStateOf java.io  padding java.io  
plusAssign java.io  port java.io  println java.io  provideDelegate java.io  remember java.io  	removeAll java.io  removePrefix java.io  replace java.io  	runServer java.io  set java.io  setValue java.io  split java.io  
startsWith java.io  	substring java.io  substringAfter java.io  substringAfterLast java.io  substringBeforeLast java.io  synchronized java.io  take java.io  to java.io  toByteArray java.io  toIntOrNull java.io  toTypedArray java.io  trim java.io  
trimIndent java.io  until java.io  	uppercase java.io  use java.io  	writeText java.io  readLine java.io.BufferedReader  reset java.io.ByteArrayOutputStream  toByteArray java.io.ByteArrayOutputStream  toString java.io.ByteArrayOutputStream  write java.io.ByteArrayOutputStream  absolutePath java.io.File  canRead java.io.File  canWrite java.io.File  delete java.io.File  exists java.io.File  isFile java.io.File  length java.io.File  let java.io.File  	listFiles java.io.File  mkdirs java.io.File  name java.io.File  
parentFile java.io.File  setWritable java.io.File  	writeText java.io.File  read java.io.FileInputStream  use java.io.FileInputStream  close java.io.FileOutputStream  write java.io.FileOutputStream  message java.io.IOException  bufferedReader java.io.InputStream  read java.io.InputStream  flush java.io.OutputStream  write java.io.OutputStream  Class 	java.lang  	Exception 	java.lang  Runnable 	java.lang  
StringBuilder 	java.lang  Thread 	java.lang  
simpleName java.lang.Class  	javaClass java.lang.Exception  message java.lang.Exception  printStackTrace java.lang.Exception  <SAM-CONSTRUCTOR> java.lang.Runnable  append java.lang.StringBuilder  toString java.lang.StringBuilder  currentTimeMillis java.lang.System  	interrupt java.lang.Thread  isAlive java.lang.Thread  sleep java.lang.Thread  start java.lang.Thread  ActivityCompat java.net  	Alignment java.net  Arrangement java.net  Boolean java.net  Bundle java.net  Button java.net  	ByteArray java.net  ByteArrayOutputStream java.net  Card java.net  CardDefaults java.net  Charsets java.net  Column java.net  ComponentActivity java.net  
Composable java.net  
ContextCompat java.net  Date java.net  Environment java.net  	Exception java.net  File java.net  FileInfo java.net  FileInputStream java.net  FileOutputStream java.net  FileUploadContext java.net  
FontWeight java.net  HttpRequest java.net  IOException java.net  Inet4Address java.net  InetSocketAddress java.net  InputStream java.net  Int java.net  LaunchedEffect java.net  
LazyColumn java.net  Locale java.net  Long java.net  Manifest java.net  Map java.net  
MaterialTheme java.net  Modifier java.net  MultipartState java.net  NetworkInterface java.net  OutputStream java.net  PackageManager java.net  Regex java.net  RegexOption java.net  RobustHttpServer java.net  Row java.net  ServerSocket java.net  SimpleDateFormat java.net  Socket java.net  SocketTimeoutException java.net  String java.net  
StringBuilder java.net  SuppressLint java.net  System java.net  Text java.net  Thread java.net  Toast java.net  Triple java.net  
URLDecoder java.net  
URLEncoder java.net  Unit java.net  WiFiTransferApp java.net  also java.net  android java.net  any java.net  apply java.net  bufferedReader java.net  byteArrayOf java.net  
cardColors java.net  charArrayOf java.net  code java.net  
component1 java.net  
component2 java.net  contains java.net  copyOf java.net  	emptyList java.net  fillMaxSize java.net  fillMaxWidth java.net  forEach java.net  format java.net  formatFileSize java.net  getValue java.net  getWiFiIPAddress java.net  indexOf java.net  indices java.net  invoke java.net  isBlank java.net  isEmpty java.net  
isNotEmpty java.net  iterator java.net  	javaClass java.net  joinToString java.net  lastIndexOf java.net  let java.net  listOf java.net  	lowercase java.net  map java.net  mapOf java.net  maxOf java.net  minOf java.net  
mutableListOf java.net  mutableMapOf java.net  mutableSetOf java.net  mutableStateOf java.net  padding java.net  
plusAssign java.net  port java.net  println java.net  provideDelegate java.net  remember java.net  	removeAll java.net  removePrefix java.net  replace java.net  	runServer java.net  set java.net  setValue java.net  split java.net  
startsWith java.net  	substring java.net  substringAfter java.net  substringAfterLast java.net  substringBeforeLast java.net  synchronized java.net  take java.net  to java.net  toByteArray java.net  toIntOrNull java.net  toTypedArray java.net  trim java.net  
trimIndent java.net  until java.net  	uppercase java.net  use java.net  	writeText java.net  hostAddress java.net.Inet4Address  isLoopbackAddress java.net.InetAddress  getNetworkInterfaces java.net.NetworkInterface  
inetAddresses java.net.NetworkInterface  
isLoopback java.net.NetworkInterface  isUp java.net.NetworkInterface  InetSocketAddress java.net.ServerSocket  accept java.net.ServerSocket  apply java.net.ServerSocket  bind java.net.ServerSocket  close java.net.ServerSocket  isClosed java.net.ServerSocket  port java.net.ServerSocket  reuseAddress java.net.ServerSocket  	soTimeout java.net.ServerSocket  use java.net.ServerSocket  close java.net.Socket  getInputStream java.net.Socket  getOutputStream java.net.Socket  	keepAlive java.net.Socket  remoteSocketAddress java.net.Socket  	soTimeout java.net.Socket  
tcpNoDelay java.net.Socket  decode java.net.URLDecoder  encode java.net.URLEncoder  Charset java.nio.charset  SimpleDateFormat 	java.text  format java.text.DateFormat  format java.text.Format  format java.text.SimpleDateFormat  ActivityCompat 	java.util  	Alignment 	java.util  Arrangement 	java.util  Boolean 	java.util  Bundle 	java.util  Button 	java.util  	ByteArray 	java.util  ByteArrayOutputStream 	java.util  Card 	java.util  CardDefaults 	java.util  Charsets 	java.util  Column 	java.util  ComponentActivity 	java.util  
Composable 	java.util  
ContextCompat 	java.util  Date 	java.util  Environment 	java.util  	Exception 	java.util  File 	java.util  FileInfo 	java.util  FileInputStream 	java.util  FileOutputStream 	java.util  FileUploadContext 	java.util  
FontWeight 	java.util  HttpRequest 	java.util  IOException 	java.util  Inet4Address 	java.util  InetSocketAddress 	java.util  InputStream 	java.util  Int 	java.util  LaunchedEffect 	java.util  
LazyColumn 	java.util  Locale 	java.util  Long 	java.util  Manifest 	java.util  Map 	java.util  
MaterialTheme 	java.util  Modifier 	java.util  MultipartState 	java.util  NetworkInterface 	java.util  OutputStream 	java.util  PackageManager 	java.util  Regex 	java.util  RegexOption 	java.util  RobustHttpServer 	java.util  Row 	java.util  ServerSocket 	java.util  SimpleDateFormat 	java.util  Socket 	java.util  SocketTimeoutException 	java.util  String 	java.util  
StringBuilder 	java.util  SuppressLint 	java.util  System 	java.util  Text 	java.util  Thread 	java.util  Toast 	java.util  Triple 	java.util  
URLDecoder 	java.util  
URLEncoder 	java.util  Unit 	java.util  WiFiTransferApp 	java.util  also 	java.util  android 	java.util  any 	java.util  apply 	java.util  bufferedReader 	java.util  byteArrayOf 	java.util  
cardColors 	java.util  charArrayOf 	java.util  code 	java.util  
component1 	java.util  
component2 	java.util  contains 	java.util  copyOf 	java.util  	emptyList 	java.util  fillMaxSize 	java.util  fillMaxWidth 	java.util  forEach 	java.util  format 	java.util  formatFileSize 	java.util  getValue 	java.util  getWiFiIPAddress 	java.util  indexOf 	java.util  indices 	java.util  invoke 	java.util  isBlank 	java.util  isEmpty 	java.util  
isNotEmpty 	java.util  iterator 	java.util  	javaClass 	java.util  joinToString 	java.util  lastIndexOf 	java.util  let 	java.util  listOf 	java.util  	lowercase 	java.util  map 	java.util  mapOf 	java.util  maxOf 	java.util  minOf 	java.util  
mutableListOf 	java.util  mutableMapOf 	java.util  mutableSetOf 	java.util  mutableStateOf 	java.util  padding 	java.util  
plusAssign 	java.util  port 	java.util  println 	java.util  provideDelegate 	java.util  remember 	java.util  	removeAll 	java.util  removePrefix 	java.util  replace 	java.util  	runServer 	java.util  set 	java.util  setValue 	java.util  split 	java.util  
startsWith 	java.util  	substring 	java.util  substringAfter 	java.util  substringAfterLast 	java.util  substringBeforeLast 	java.util  synchronized 	java.util  take 	java.util  to 	java.util  toByteArray 	java.util  toIntOrNull 	java.util  toTypedArray 	java.util  trim 	java.util  
trimIndent 	java.util  until 	java.util  	uppercase 	java.util  use 	java.util  	writeText 	java.util  
getDefault java.util.Locale  Array kotlin  BooleanArray kotlin  	ByteArray kotlin  	CharArray kotlin  CharSequence kotlin  DoubleArray kotlin  
FloatArray kotlin  	Function0 kotlin  	Function1 kotlin  IntArray kotlin  	LongArray kotlin  Nothing kotlin  Pair kotlin  Result kotlin  
ShortArray kotlin  String kotlin  Triple kotlin  
UByteArray kotlin  	UIntArray kotlin  
ULongArray kotlin  UShortArray kotlin  also kotlin  apply kotlin  byteArrayOf kotlin  charArrayOf kotlin  code kotlin  let kotlin  map kotlin  synchronized kotlin  to kotlin  use kotlin  toString 
kotlin.Any  not kotlin.Boolean  toString kotlin.Boolean  copyOf kotlin.ByteArray  get kotlin.ByteArray  indices kotlin.ByteArray  size kotlin.ByteArray  code kotlin.Char  iterator kotlin.CharArray  isEmpty kotlin.CharSequence  div 
kotlin.Double  sp 
kotlin.Double  times 
kotlin.Double  toInt 
kotlin.Double  invoke kotlin.Function0  also 
kotlin.Int  	compareTo 
kotlin.Int  div 
kotlin.Int  inc 
kotlin.Int  minus 
kotlin.Int  plus 
kotlin.Int  
plusAssign 
kotlin.Int  rangeTo 
kotlin.Int  rem 
kotlin.Int  times 
kotlin.Int  toByte 
kotlin.Int  toChar 
kotlin.Int  toDouble 
kotlin.Int  toLong 
kotlin.Int  
unaryMinus 
kotlin.Int  until 
kotlin.Int  	compareTo kotlin.Long  minus kotlin.Long  plus kotlin.Long  
plusAssign kotlin.Long  toDouble kotlin.Long  	Companion 
kotlin.String  contains 
kotlin.String  format 
kotlin.String  get 
kotlin.String  indexOf 
kotlin.String  invoke 
kotlin.String  isBlank 
kotlin.String  isEmpty 
kotlin.String  
isNotEmpty 
kotlin.String  lastIndexOf 
kotlin.String  length 
kotlin.String  	lowercase 
kotlin.String  plus 
kotlin.String  removePrefix 
kotlin.String  replace 
kotlin.String  split 
kotlin.String  
startsWith 
kotlin.String  	substring 
kotlin.String  substringAfter 
kotlin.String  substringAfterLast 
kotlin.String  substringBeforeLast 
kotlin.String  take 
kotlin.String  to 
kotlin.String  toByteArray 
kotlin.String  toIntOrNull 
kotlin.String  trim 
kotlin.String  
trimIndent 
kotlin.String  	uppercase 
kotlin.String  format kotlin.String.Companion  invoke kotlin.String.Companion  message kotlin.Throwable  printStackTrace kotlin.Throwable  
component1 
kotlin.Triple  
component2 
kotlin.Triple  
component3 
kotlin.Triple  ByteIterator kotlin.collections  CharIterator kotlin.collections  IntIterator kotlin.collections  Iterator kotlin.collections  List kotlin.collections  Map kotlin.collections  MutableIterator kotlin.collections  MutableList kotlin.collections  
MutableMap kotlin.collections  
MutableSet kotlin.collections  any kotlin.collections  
component1 kotlin.collections  
component2 kotlin.collections  contains kotlin.collections  copyOf kotlin.collections  	emptyList kotlin.collections  forEach kotlin.collections  indexOf kotlin.collections  indices kotlin.collections  isEmpty kotlin.collections  
isNotEmpty kotlin.collections  iterator kotlin.collections  joinToString kotlin.collections  lastIndexOf kotlin.collections  listOf kotlin.collections  map kotlin.collections  mapOf kotlin.collections  maxOf kotlin.collections  minOf kotlin.collections  mutableIterator kotlin.collections  
mutableListOf kotlin.collections  mutableMapOf kotlin.collections  mutableSetOf kotlin.collections  
plusAssign kotlin.collections  	removeAll kotlin.collections  set kotlin.collections  take kotlin.collections  toByteArray kotlin.collections  toTypedArray kotlin.collections  hasNext kotlin.collections.CharIterator  next kotlin.collections.CharIterator  hasNext kotlin.collections.IntIterator  next kotlin.collections.IntIterator  hasNext kotlin.collections.Iterator  next kotlin.collections.Iterator  any kotlin.collections.List  get kotlin.collections.List  
isNotEmpty kotlin.collections.List  iterator kotlin.collections.List  joinToString kotlin.collections.List  map kotlin.collections.List  size kotlin.collections.List  Entry kotlin.collections.Map  get kotlin.collections.Map  hasNext "kotlin.collections.MutableIterator  next "kotlin.collections.MutableIterator  add kotlin.collections.MutableList  
isNotEmpty kotlin.collections.MutableList  iterator kotlin.collections.MutableList  toTypedArray kotlin.collections.MutableList  MutableEntry kotlin.collections.MutableMap  get kotlin.collections.MutableMap  iterator kotlin.collections.MutableMap  set kotlin.collections.MutableMap  size kotlin.collections.MutableMap  
component1 *kotlin.collections.MutableMap.MutableEntry  
component2 *kotlin.collections.MutableMap.MutableEntry  add kotlin.collections.MutableSet  clear kotlin.collections.MutableSet  
isNotEmpty kotlin.collections.MutableSet  	removeAll kotlin.collections.MutableSet  size kotlin.collections.MutableSet  maxOf kotlin.comparisons  minOf kotlin.comparisons  SuspendFunction1 kotlin.coroutines  bufferedReader 	kotlin.io  iterator 	kotlin.io  println 	kotlin.io  
startsWith 	kotlin.io  use 	kotlin.io  	writeText 	kotlin.io  	javaClass 
kotlin.jvm  	CharRange 
kotlin.ranges  IntRange 
kotlin.ranges  	LongRange 
kotlin.ranges  	UIntRange 
kotlin.ranges  
ULongRange 
kotlin.ranges  contains 
kotlin.ranges  until 
kotlin.ranges  iterator kotlin.ranges.IntProgression  iterator kotlin.ranges.IntRange  KClass kotlin.reflect  
KFunction0 kotlin.reflect  KMutableProperty0 kotlin.reflect  
KProperty0 kotlin.reflect  Sequence kotlin.sequences  any kotlin.sequences  contains kotlin.sequences  forEach kotlin.sequences  indexOf kotlin.sequences  iterator kotlin.sequences  joinToString kotlin.sequences  lastIndexOf kotlin.sequences  map kotlin.sequences  maxOf kotlin.sequences  minOf kotlin.sequences  take kotlin.sequences  Charsets kotlin.text  MatchResult kotlin.text  Regex kotlin.text  RegexOption kotlin.text  String kotlin.text  any kotlin.text  contains kotlin.text  forEach kotlin.text  format kotlin.text  indexOf kotlin.text  indices kotlin.text  isBlank kotlin.text  isEmpty kotlin.text  
isNotEmpty kotlin.text  iterator kotlin.text  lastIndexOf kotlin.text  	lowercase kotlin.text  map kotlin.text  maxOf kotlin.text  minOf kotlin.text  removePrefix kotlin.text  replace kotlin.text  set kotlin.text  split kotlin.text  
startsWith kotlin.text  	substring kotlin.text  substringAfter kotlin.text  substringAfterLast kotlin.text  substringBeforeLast kotlin.text  take kotlin.text  toByteArray kotlin.text  toIntOrNull kotlin.text  trim kotlin.text  
trimIndent kotlin.text  	uppercase kotlin.text  UTF_8 kotlin.text.Charsets  groupValues kotlin.text.MatchResult  find kotlin.text.Regex  IGNORE_CASE kotlin.text.RegexOption  CoroutineScope kotlinx.coroutines  getWiFiIPAddress !kotlinx.coroutines.CoroutineScope  println android.app.Activity  println android.content.Context  println android.content.ContextWrapper  println  android.view.ContextThemeWrapper  println #androidx.activity.ComponentActivity  LocalContext "androidx.compose.foundation.layout  MainActivity "androidx.compose.foundation.layout  Spacer "androidx.compose.foundation.layout  
appendLine "androidx.compose.foundation.layout  buildString "androidx.compose.foundation.layout  downloadDir "androidx.compose.foundation.layout  width "androidx.compose.foundation.layout  LocalContext .androidx.compose.foundation.layout.ColumnScope  Spacer .androidx.compose.foundation.layout.ColumnScope  Toast .androidx.compose.foundation.layout.ColumnScope  println .androidx.compose.foundation.layout.ColumnScope  width .androidx.compose.foundation.layout.ColumnScope  LocalContext +androidx.compose.foundation.layout.RowScope  Modifier +androidx.compose.foundation.layout.RowScope  Spacer +androidx.compose.foundation.layout.RowScope  Toast +androidx.compose.foundation.layout.RowScope  dp +androidx.compose.foundation.layout.RowScope  println +androidx.compose.foundation.layout.RowScope  width +androidx.compose.foundation.layout.RowScope  LocalContext androidx.compose.material3  MainActivity androidx.compose.material3  Spacer androidx.compose.material3  
appendLine androidx.compose.material3  buildString androidx.compose.material3  downloadDir androidx.compose.material3  width androidx.compose.material3  LocalContext androidx.compose.runtime  MainActivity androidx.compose.runtime  Spacer androidx.compose.runtime  
appendLine androidx.compose.runtime  buildString androidx.compose.runtime  downloadDir androidx.compose.runtime  width androidx.compose.runtime  width androidx.compose.ui.Modifier  width &androidx.compose.ui.Modifier.Companion  println #androidx.core.app.ComponentActivity  LocalContext com.xeno.wififiletransfer  Spacer com.xeno.wififiletransfer  
appendLine com.xeno.wififiletransfer  buildString com.xeno.wififiletransfer  downloadDir com.xeno.wififiletransfer  width com.xeno.wififiletransfer  getServerDebugInfo &com.xeno.wififiletransfer.MainActivity  println &com.xeno.wififiletransfer.MainActivity  
appendLine *com.xeno.wififiletransfer.RobustHttpServer  buildString *com.xeno.wififiletransfer.RobustHttpServer  getDownloadDirectoryInfo *com.xeno.wififiletransfer.RobustHttpServer  LocalContext java.io  MainActivity java.io  Spacer java.io  
appendLine java.io  buildString java.io  downloadDir java.io  width java.io  flush java.io.FileOutputStream  
Appendable 	java.lang  
appendLine java.lang.StringBuilder  downloadDir java.lang.StringBuilder  forEach java.lang.StringBuilder  LocalContext java.net  MainActivity java.net  Spacer java.net  
appendLine java.net  buildString java.net  downloadDir java.net  width java.net  LocalContext 	java.util  MainActivity 	java.util  Spacer 	java.util  
appendLine 	java.util  buildString 	java.util  downloadDir 	java.util  width 	java.util  
appendLine kotlin.text  buildString kotlin.text  Byte "androidx.compose.foundation.layout  Byte androidx.compose.material3  Byte androidx.compose.runtime  Byte com.xeno.wififiletransfer  Byte *com.xeno.wififiletransfer.RobustHttpServer  Byte java.io  printStackTrace java.io.IOException  Byte java.net  Byte 	java.util  toByteArray kotlin.collections.MutableList  handleLegacyMultipartUpload *com.xeno.wififiletransfer.RobustHttpServer                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                           
@echo off
echo Creating Android keystore for WiFi File Transfer app...
echo.

REM Try to find keytool in common Android Studio locations
set KEYTOOL_PATH=""
if exist "C:\Program Files\Android\Android Studio\jbr\bin\keytool.exe" (
    set KEYTOOL_PATH="C:\Program Files\Android\Android Studio\jbr\bin\keytool.exe"
) else if exist "C:\Users\<USER>\AppData\Local\Android\Sdk\jbr\bin\keytool.exe" (
    set KEYTOOL_PATH="C:\Users\<USER>\AppData\Local\Android\Sdk\jbr\bin\keytool.exe"
) else if exist "C:\Android\Android Studio\jbr\bin\keytool.exe" (
    set KEYTOOL_PATH="C:\Android\Android Studio\jbr\bin\keytool.exe"
) else (
    REM Try system PATH
    keytool -help >nul 2>&1
    if not errorlevel 1 (
        set KEYTOOL_PATH="keytool"
    ) else (
        echo ERROR: keytool not found!
        echo Please make sure Android Studio is installed or Java JDK is in PATH
        echo.
        echo Searched locations:
        echo - C:\Program Files\Android\Android Studio\jbr\bin\keytool.exe
        echo - C:\Users\<USER>\AppData\Local\Android\Sdk\jbr\bin\keytool.exe
        echo - C:\Android\Android Studio\jbr\bin\keytool.exe
        echo - System PATH
        pause
        exit /b 1
    )
)

echo Found keytool at: %KEYTOOL_PATH%
echo.

REM Set keystore details
set KEYSTORE_NAME=wififiletransfer-release.jks
set KEY_ALIAS=wififiletransfer
set VALIDITY_DAYS=10000

echo This will create a keystore file: %KEYSTORE_NAME%
echo Key alias: %KEY_ALIAS%
echo Validity: %VALIDITY_DAYS% days (about 27 years)
echo.
echo You will be prompted for:
echo - Keystore password (remember this!)
echo - Key password (can be same as keystore password)
echo - Your name and organization details
echo.
pause

REM Generate the keystore
%KEYTOOL_PATH% -genkey -v -keystore %KEYSTORE_NAME% -alias %KEY_ALIAS% -keyalg RSA -keysize 2048 -validity %VALIDITY_DAYS%

if errorlevel 1 (
    echo.
    echo ERROR: Failed to create keystore
    pause
    exit /b 1
)

echo.
echo SUCCESS: Keystore created successfully!
echo.
echo File created: %KEYSTORE_NAME%
echo.
echo IMPORTANT:
echo 1. Keep this keystore file safe - you'll need it for app updates
echo 2. Remember your passwords - they cannot be recovered
echo 3. Add the keystore to your build.gradle.kts configuration
echo.
echo Next steps:
echo 1. Move %KEYSTORE_NAME% to your app/ directory
echo 2. Update build.gradle.kts with your keystore details
echo 3. Build a release APK
echo.
pause

// Add this to your app/build.gradle.kts file in the signingConfigs section:

signingConfigs {
    create("release") {
        storeFile = file("wififiletransfer-release.jks")
        storePassword = "YOUR_STORE_PASSWORD"
        keyAlias = "wififiletransfer"
        keyPassword = "YOUR_KEY_PASSWORD"
    }
}

buildTypes {
    release {
        isMinifyEnabled = false
        proguardFiles(
            getDefaultProguardFile("proguard-android-optimize.txt"),
            "proguard-rules.pro"
        )
        signingConfig = signingConfigs.getByName("release")
    }
}

// Alternative: Using environment variables (more secure for CI/CD)
signingConfigs {
    create("release") {
        storeFile = file("wififiletransfer-release.jks")
        storePassword = System.getenv("KEYSTORE_PASSWORD")
        keyAlias = "wififiletransfer"
        keyPassword = System.getenv("KEY_PASSWORD")
    }
}

// Alternative: Using gradle.properties (add to gradle.properties file)
// KEYSTORE_PASSWORD=your_password
// KEY_PASSWORD=your_password

signingConfigs {
    create("release") {
        storeFile = file("wififiletransfer-release.jks")
        storePassword = project.findProperty("KEYSTORE_PASSWORD") as String? ?: ""
        keyAlias = "wififiletransfer"
        keyPassword = project.findProperty("KEY_PASSWORD") as String? ?: ""
    }
}

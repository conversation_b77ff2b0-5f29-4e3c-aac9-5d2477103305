@echo off
echo WiFi File Transfer - Network Diagnostics
echo ========================================
echo.

echo 1. Checking your PC's network configuration:
echo.
echo Active network adapters:
ipconfig | findstr /C:"Wireless LAN adapter" /C:"Ethernet adapter" /C:"IPv4 Address"

echo.
echo 2. Checking WiFi connection:
netsh wlan show profiles

echo.
echo 3. Checking current WiFi network:
netsh wlan show interfaces | findstr /C:"Profile" /C:"State" /C:"SSID"

echo.
echo 4. Checking Windows Firewall status:
netsh advfirewall show allprofiles state

echo.
echo 5. Checking if any process is using port 8080 on your PC:
netstat -an | findstr :8080

echo.
echo 6. Testing local network connectivity:
echo Checking if you can reach your router...
for /f "tokens=3" %%i in ('route print ^| findstr "0.0.0.0.*0.0.0.0"') do (
    echo Default gateway: %%i
    ping -n 2 %%i
)

echo.
echo ========================================
echo Diagnostics completed.
echo.
echo Common solutions:
echo 1. Make sure both devices are on the same WiFi network
echo 2. Check if Windows Firewall is blocking the connection
echo 3. Try disabling antivirus temporarily
echo 4. Restart your router if needed
echo 5. Make sure the Android app shows "Server is running"
echo.
pause

@echo off
echo WiFi File Transfer - Connection Test
echo =====================================
echo.

REM Get the Android device IP from user
set /p ANDROID_IP="Enter the IP address shown in the Android app (e.g., *************): "

if "%ANDROID_IP%"=="" (
    echo Error: No IP address provided
    pause
    exit /b 1
)

echo.
echo Testing connection to %ANDROID_IP%...
echo.

echo 1. Testing basic network connectivity (ping):
ping -n 4 %ANDROID_IP%

echo.
echo 2. Testing if port 8080 is reachable:
echo Attempting to connect to %ANDROID_IP%:8080...

REM Try to connect to the port using telnet (if available)
telnet %ANDROID_IP% 8080 2>nul
if errorlevel 1 (
    echo Port 8080 connection failed or telnet not available
    echo.
    echo 3. Testing with PowerShell (more reliable):
    powershell -Command "try { $socket = New-Object System.Net.Sockets.TcpClient; $socket.Connect('%ANDROID_IP%', 8080); $socket.Close(); Write-Host 'SUCCESS: Port 8080 is reachable!' -ForegroundColor Green } catch { Write-Host 'FAILED: Cannot connect to port 8080' -ForegroundColor Red; Write-Host $_.Exception.Message }"
) else (
    echo SUCCESS: Port 8080 is reachable!
)

echo.
echo 4. Your PC's IP addresses:
ipconfig | findstr "IPv4"

echo.
echo 5. Testing HTTP connection:
echo Trying to fetch the webpage...
powershell -Command "try { $response = Invoke-WebRequest -Uri 'http://%ANDROID_IP%:8080' -TimeoutSec 10; Write-Host 'SUCCESS: HTTP server responded!' -ForegroundColor Green; Write-Host 'Response length:' $response.Content.Length 'bytes' } catch { Write-Host 'FAILED: HTTP request failed' -ForegroundColor Red; Write-Host $_.Exception.Message }"

echo.
echo =====================================
echo Test completed. 
echo.
echo If all tests pass but browser still fails:
echo 1. Try a different browser (Chrome, Firefox, Edge)
echo 2. Clear browser cache
echo 3. Try incognito/private mode
echo 4. Check Windows Firewall settings
echo 5. Restart the server in the Android app
echo.
pause

@echo off
echo Creating Android keystore automatically...
echo.

REM Find keytool
set KEYTOOL_PATH=""
if exist "C:\Program Files\Android\Android Studio\jbr\bin\keytool.exe" (
    set KEYTOOL_PATH="C:\Program Files\Android\Android Studio\jbr\bin\keytool.exe"
) else if exist "C:\Users\<USER>\AppData\Local\Android\Sdk\jbr\bin\keytool.exe" (
    set KEYTOOL_PATH="C:\Users\<USER>\AppData\Local\Android\Sdk\jbr\bin\keytool.exe"
) else (
    echo ERROR: keytool not found!
    pause
    exit /b 1
)

echo Found keytool at: %KEYTOOL_PATH%

REM Set keystore details
set KEYSTORE_NAME=app\wififiletransfer-release.jks
set KEY_ALIAS=wififiletransfer
set VALIDITY_DAYS=10000
set STORE_PASS=wififiletransfer123
set KEY_PASS=wififiletransfer123

echo.
echo Creating keystore with these details:
echo - File: %KEYSTORE_NAME%
echo - Alias: %KEY_ALIAS%
echo - Store Password: %STORE_PASS%
echo - Key Password: %KEY_PASS%
echo - Validity: %VALIDITY_DAYS% days
echo.

REM Create the keystore automatically
%KEYTOOL_PATH% -genkeypair -v ^
    -keystore %KEYSTORE_NAME% ^
    -alias %KEY_ALIAS% ^
    -keyalg RSA ^
    -keysize 2048 ^
    -validity %VALIDITY_DAYS% ^
    -storepass %STORE_PASS% ^
    -keypass %KEY_PASS% ^
    -dname "CN=WiFi File Transfer, OU=Development, O=Personal, L=Unknown, S=Unknown, C=US"

if errorlevel 1 (
    echo.
    echo ERROR: Failed to create keystore
    pause
    exit /b 1
)

echo.
echo SUCCESS: Keystore created successfully!
echo.
echo File: %KEYSTORE_NAME%
echo Store Password: %STORE_PASS%
echo Key Password: %KEY_PASS%
echo Key Alias: %KEY_ALIAS%
echo.
echo IMPORTANT: Save these passwords! You'll need them to sign your app.
echo.
echo Next step: Update your build.gradle.kts file with these details.
echo.
pause
